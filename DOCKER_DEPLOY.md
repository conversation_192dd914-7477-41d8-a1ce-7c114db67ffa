# 房源爬虫项目 Docker 部署指南

## 项目架构

```
房源爬虫系统
├── 前端 (Vue 3 + Nginx) - 端口 8891
├── 后端 (Bun + Elysia) - 端口 3000
└── 数据库 (PostgreSQL 15) - 外部服务器 ali.qianluo.top:5555
```

## 快速开始

### 1. 环境要求

- Docker 20.10+
- docker-compose 1.29+
- 至少 2GB 可用内存
- 至少 5GB 可用磁盘空间

### 2. 一键部署

```bash
# 给部署脚本执行权限
chmod +x deploy.sh

# 启动所有服务
./deploy.sh start
```

### 3. 访问应用

- **前端界面**: http://localhost:8891
- **后端API**: http://localhost:3000
- **数据库**: ali.qianluo.top:5555

## 详细部署步骤

### 1. 克隆项目

```bash
git clone <your-repo-url>
cd house_spider
```

### 2. 配置环境变量

编辑 `.env.docker` 文件，修改数据库密码等配置：

```bash
# PostgreSQL数据库配置
POSTGRES_DB=house_spider
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your_secure_password

# 爬虫配置
SCRAPER_DELAY_MIN=2000
SCRAPER_DELAY_MAX=5000
```

### 3. 启动服务

```bash
# 方式1: 使用部署脚本（推荐）


# 方式2: 直接使用docker-compose
docker-compose --env-file .env.docker up -d --build
```

### 4. 数据库迁移

```bash
# 等待数据库启动后执行迁移
./deploy.sh migrate

# 或者手动执行
docker-compose exec backend bunx prisma migrate deploy
```

## 管理命令

### 服务管理

```bash
# 启动服务
./deploy.sh start

# 停止服务
./deploy.sh stop

# 重启服务
./deploy.sh restart

# 查看服务状态
./deploy.sh status
```

### 日志查看

```bash
# 查看所有服务日志
./deploy.sh logs

# 查看特定服务日志
./deploy.sh logs backend
./deploy.sh logs frontend
# 注意：PostgreSQL运行在外部服务器，无法通过docker-compose查看日志
```

### 数据库操作

```bash
# 执行数据库迁移
./deploy.sh migrate

# 连接到数据库 (需要安装PostgreSQL客户端)
psql -h ali.qianluo.top -p 5555 -U postgres -d house_spider
```

### 清理数据

```bash
# 清理所有数据（谨慎使用）
./deploy.sh clean
```

## 服务配置

### 前端服务 (Nginx)

- 端口: 8891 (外部) -> 80 (内部)
- 配置文件: `front/nginx.conf`
- 静态文件: `/usr/share/nginx/html`
- API代理: `/api/*` -> `http://backend:3000/`

### 后端服务 (Bun)

- 端口: 3000
- 运行时: Bun 1.1.38
- 数据库: Prisma ORM
- 日志目录: `backend/logs`

### 数据库服务 (PostgreSQL)

- 主机: ali.qianluo.top
- 端口: 5555
- 版本: PostgreSQL 15
- 用户: postgres
- 密码: 18755870538
- 数据库: house_spider
- 注意: 使用外部PostgreSQL服务器，不在Docker容器中运行

## 监控和维护

### 查看容器状态

```bash
docker-compose ps
```

### 查看资源使用

```bash
docker stats house-spider-frontend house-spider-backend
```

### 备份数据库

```bash
# 创建数据库备份 (需要安装PostgreSQL客户端)
pg_dump -h ali.qianluo.top -p 5555 -U postgres house_spider > backup_$(date +%Y%m%d_%H%M%S).sql

# 恢复数据库备份
psql -h ali.qianluo.top -p 5555 -U postgres -d house_spider < backup_file.sql
```

### 更新应用

```bash
# 拉取最新代码
git pull

# 重新构建并启动
./deploy.sh restart
```

## 故障排除

### 常见问题

1. **端口冲突**
   ```bash
   # 检查端口占用
   netstat -tulpn | grep :8891
   netstat -tulpn | grep :3000
   # PostgreSQL运行在外部服务器，无需检查本地端口
   ```

2. **数据库连接失败**
   ```bash
   # 测试数据库连接
   docker-compose exec backend bunx prisma db pull

   # 或者直接测试外部数据库连接
   psql -h ali.qianluo.top -p 5555 -U postgres -d house_spider -c "SELECT 1;"
   ```

3. **前端无法访问后端**
   ```bash
   # 检查网络连接
   docker-compose exec frontend ping backend
   
   # 检查Nginx配置
   docker-compose exec frontend nginx -t
   ```

### 日志位置

- 前端日志: `docker-compose logs frontend`
- 后端日志: `docker-compose logs backend` 或 `backend/logs/`
- 数据库日志: 联系数据库管理员查看外部PostgreSQL服务器日志

## 生产环境建议

1. **安全配置**
   - 修改默认密码
   - 使用HTTPS
   - 配置防火墙

2. **性能优化**
   - 增加MySQL内存配置
   - 配置Nginx缓存
   - 使用Redis缓存

3. **监控告警**
   - 配置日志收集
   - 设置资源监控
   - 配置健康检查

## 技术支持

如有问题，请查看：
1. 项目日志: `./deploy.sh logs`
2. 服务状态: `./deploy.sh status`
3. Docker文档: https://docs.docker.com/
