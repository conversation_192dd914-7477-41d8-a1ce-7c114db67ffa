import { ScraperManager } from './src/scrapers';

async function exampleUsage() {
  const scraperManager = new ScraperManager();
  
  console.log('=== 南京安居客爬虫使用示例 ===\n');
  
  try {
    // 示例1: 爬取特定区域的房源
    console.log('示例1: 爬取百家湖区域的110-100平方房源...');
    await scraperManager.runNanjingAnjukeByArea('百家湖', '110-100平方', 2);
    
    // 示例2: 爬取所有支持区域的房源
    console.log('\n示例2: 爬取所有区域的房源（每个区域2页）...');
    await scraperManager.runNanjingAnjukeOnly(undefined, 2);
    
    // 示例3: 爬取特定面积筛选的房源
    console.log('\n示例3: 爬取所有区域的110-130平方房源...');
    await scraperManager.runNanjingAnjukeOnly('110-130平方', 1);
    
    console.log('\n✅ 所有示例执行完成！');
    
  } catch (error) {
    console.error('❌ 执行过程中出现错误:', error);
  }
}

// 如果直接运行此文件，则执行示例
if (require.main === module) {
  exampleUsage().catch(console.error);
}
