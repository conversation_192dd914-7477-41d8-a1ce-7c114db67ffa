<template>
  <div class="pagination-controls">
    <!-- 分页信息 -->
    <div class="pagination-info">
      <span class="pagination-summary">
        共 {{ pagination.total }} 条记录，
        第 {{ pagination.page }} / {{ pagination.totalPages }} 页
      </span>
      <span class="pagination-range">
        显示第 {{ startRecord }} - {{ endRecord }} 条
      </span>
    </div>
    
    <!-- 主分页组件 -->
    <a-pagination
      :current="pagination.page"
      :total="pagination.total"
      :page-size="pagination.limit"
      :show-total="showTotal"
      :show-jumper="showJumper"
      :show-page-size="showPageSize"
      :page-size-options="pageSizeOptions"
      :disabled="loading"
      :simple="simple"
      @change="handlePageChange"
      @page-size-change="handlePageSizeChange"
    >
      <template v-if="showTotal" #total="{ total }">
        <span class="pagination-total">
          共 {{ total }} 条，每页 {{ pagination.limit }} 条
        </span>
      </template>
    </a-pagination>
    
    <!-- 快速导航按钮 -->
    <div v-if="showQuickNav" class="pagination-quick-nav">
      <a-space>
        <a-button 
          size="small" 
          :disabled="!hasPrevPage || loading"
          @click="goToFirstPage"
        >
          首页
        </a-button>
        <a-button 
          size="small" 
          :disabled="!hasPrevPage || loading"
          @click="prevPage"
        >
          上一页
        </a-button>
        <a-button 
          size="small" 
          :disabled="!hasNextPage || loading"
          @click="nextPage"
        >
          下一页
        </a-button>
        <a-button 
          size="small" 
          :disabled="!hasNextPage || loading"
          @click="goToLastPage"
        >
          末页
        </a-button>
      </a-space>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

interface PaginationData {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

interface Props {
  pagination: PaginationData;
  loading?: boolean;
  showTotal?: boolean;
  showJumper?: boolean;
  showPageSize?: boolean;
  showQuickNav?: boolean;
  simple?: boolean;
  pageSizeOptions?: number[];
}

interface Emits {
  (e: 'page-change', page: number): void;
  (e: 'page-size-change', pageSize: number): void;
  (e: 'first-page'): void;
  (e: 'last-page'): void;
  (e: 'next-page'): void;
  (e: 'prev-page'): void;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  showTotal: true,
  showJumper: true,
  showPageSize: true,
  showQuickNav: false,
  simple: false,
  pageSizeOptions: () => [10, 20, 50, 100]
});

const emit = defineEmits<Emits>();

// 计算属性
const hasNextPage = computed(() => props.pagination.page < props.pagination.totalPages);
const hasPrevPage = computed(() => props.pagination.page > 1);

const startRecord = computed(() => {
  if (props.pagination.total === 0) return 0;
  return (props.pagination.page - 1) * props.pagination.limit + 1;
});

const endRecord = computed(() => {
  const end = props.pagination.page * props.pagination.limit;
  return Math.min(end, props.pagination.total);
});

// 事件处理
const handlePageChange = (page: number) => {
  emit('page-change', page);
};

const handlePageSizeChange = (pageSize: number) => {
  emit('page-size-change', pageSize);
};

const goToFirstPage = () => {
  if (hasPrevPage.value && !props.loading) {
    emit('first-page');
  }
};

const goToLastPage = () => {
  if (hasNextPage.value && !props.loading) {
    emit('last-page');
  }
};

const nextPage = () => {
  if (hasNextPage.value && !props.loading) {
    emit('next-page');
  }
};

const prevPage = () => {
  if (hasPrevPage.value && !props.loading) {
    emit('prev-page');
  }
};
</script>

<style scoped>
.pagination-controls {
  background: #fff;
  border-top: 1px solid #e5e6eb;
  padding: 24px;
}

.pagination-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 0 8px;
}

.pagination-summary {
  font-size: 14px;
  color: #4e5969;
  font-weight: 500;
}

.pagination-range {
  font-size: 12px;
  color: #86909c;
}

.pagination-total {
  font-size: 14px;
  color: #86909c;
}

.pagination-quick-nav {
  display: flex;
  justify-content: center;
  margin-top: 16px;
}

:deep(.arco-pagination) {
  display: flex;
  justify-content: center;
}

:deep(.arco-pagination-item) {
  border-radius: 6px;
}

:deep(.arco-pagination-item-active) {
  background-color: #165dff;
  border-color: #165dff;
}

:deep(.arco-pagination-jumper) {
  margin-left: 16px;
}

:deep(.arco-pagination-options) {
  margin-left: 16px;
}

@media (max-width: 768px) {
  .pagination-info {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }
  
  .pagination-quick-nav {
    margin-top: 12px;
  }
  
  .pagination-quick-nav .arco-space {
    flex-wrap: wrap;
    gap: 8px;
  }
  
  :deep(.arco-pagination) {
    flex-wrap: wrap;
    justify-content: flex-start;
  }
  
  :deep(.arco-pagination-jumper),
  :deep(.arco-pagination-options) {
    margin-left: 0;
    margin-top: 8px;
  }
}
</style>
