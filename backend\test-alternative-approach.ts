import * as cheerio from 'cheerio';

async function testAlternativeApproach() {
  // 尝试不同的URL模式
  const urls = [
    'https://nanjing.anjuke.com/sale/',
    'https://nanjing.anjuke.com/sale/jiangning/',
    'https://m.anjuke.com/nj/sale/',
    'https://m.anjuke.com/nj/sale/jiangning/',
  ];
  
  for (const url of urls) {
    console.log(`\n=== 测试 ${url} ===`);
    
    try {
      // 添加延迟模拟真实用户行为
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const response = await fetch(url, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
          'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
          'Accept-Encoding': 'gzip, deflate, br',
          'Connection': 'keep-alive',
          'Upgrade-Insecure-Requests': '1',
          'Sec-Fetch-Dest': 'document',
          'Sec-Fetch-Mode': 'navigate',
          'Sec-Fetch-Site': 'none',
          'Sec-Fetch-User': '?1',
          'Cache-Control': 'max-age=0',
        }
      });

      console.log('响应状态:', response.status);
      
      const html = await response.text();
      console.log('HTML长度:', html.length);
      
      const $ = cheerio.load(html);
      console.log('页面标题:', $('title').text());
      
      // 检查是否是验证页面
      if (html.includes('安全验证') || html.includes('verifycode') || html.includes('@@xxzlGatewayUrl') || html.length < 10000) {
        console.log('遇到安全验证页面');
        continue;
      }
      
      // 查找房源信息
      const houseKeywords = ['万', '室', '厅', '㎡', '平方'];
      let foundHouseInfo = false;
      
      for (const keyword of houseKeywords) {
        const elements = $(`*:contains("${keyword}")`);
        if (elements.length > 5) {
          console.log(`包含"${keyword}"的元素: ${elements.length}个`);
          foundHouseInfo = true;
        }
      }
      
      if (foundHouseInfo) {
        console.log('✅ 找到房源信息，这个URL可能有效');
        
        // 查找可能的房源容器
        const selectors = [
          '.property',
          '.list-item',
          '.item-mod',
          '.house-item', 
          '.prop-item',
          '.esf-item',
          '.item',
          '[data-role="item"]',
          '[class*="item"]',
          '[class*="house"]',
          '[class*="property"]'
        ];
        
        for (const selector of selectors) {
          const items = $(selector);
          if (items.length > 0) {
            console.log(`  - ${selector}: ${items.length}个元素`);
            
            // 检查第一个元素是否包含房源信息
            const firstText = items.first().text();
            const hasHouseInfo = houseKeywords.some(keyword => firstText.includes(keyword));
            if (hasHouseInfo) {
              console.log(`    ✅ 第一个${selector}元素包含房源信息`);
              console.log(`    内容预览: ${firstText.substring(0, 100)}...`);
            }
          }
        }
        
        break; // 找到有效URL就停止
      } else {
        console.log('❌ 未找到房源信息');
      }
      
    } catch (error) {
      console.error('请求失败:', error instanceof Error ? error.message : String(error));
    }
  }
}

testAlternativeApproach().catch(console.error);
