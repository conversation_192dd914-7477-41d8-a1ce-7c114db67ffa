import { BeikeScraper } from './beike';

import { NanjingAnjukeScraper } from './anjuke-nanjing';

export class ScraperManager {
  private beikeScraper: BeikeScraper;

  private nanjingAnjukeScraper: NanjingAnjukeScraper;

  constructor() {
    this.beikeScraper = new BeikeScraper();

    this.nanjingAnjukeScraper = new NanjingAnjukeScraper();
  }

  async runAllScrapers(maxPages: number = 3): Promise<void> {
    console.log('Starting scraper job...');
    
    try {
      // 爬取贝壳网数据
      console.log('Scraping Beike...');
      const beikeListings = await this.beikeScraper.scrapeMultiplePages(maxPages);
      await this.beikeScraper.saveListings(beikeListings);
      console.log(`Saved ${beikeListings.length} Beike listings`);

      // 延迟一段时间再爬取安居客
      await this.delay(5000);

      // 爬取安居客数据
      console.log('Scraping Anjuke...');
      // 爬取所有区域
      await this.runAnjukeOnly( maxPages);
     
      console.log('Scraper job completed successfully');
    } catch (error) {
      console.error('Error in scraper job:', error);
      throw error;
    }
  }

  async runBeikeOnly(maxPages: number = 3): Promise<void> {
    console.log('Starting Beike scraper...');
    try {
      const listings = await this.beikeScraper.scrapeMultiplePages(maxPages);
      await this.beikeScraper.saveListings(listings);
      console.log(`Beike scraper completed. Saved ${listings.length} listings`);
    } catch (error) {
      console.error('Error in Beike scraper:', error);
      throw error;
    }
  }

  async runAnjukeOnly(maxPages: number = 3): Promise<void> {
    console.log('Starting Anjuke scraper...');
    try {
      const allListings: any[] = [];

      // 爬取100-110平方的所有区域
      console.log('Scraping all areas with 100-110平方 filter...');
      const listings100110 = await this.nanjingAnjukeScraper.scrapeAllAreas('100-110平方', maxPages);
      allListings.push(...listings100110);
      console.log(`Scraped ${listings100110.length} listings for 100-110平方`);

      // 延迟一段时间
      await this.delay(3000);

      // 爬取110-130平方的所有区域
      console.log('Scraping all areas with 110-130平方 filter...');
      const listings110130 = await this.nanjingAnjukeScraper.scrapeAllAreas('110-130平方', maxPages);
      allListings.push(...listings110130);
      console.log(`Scraped ${listings110130.length} listings for 110-130平方`);

      // 保存所有房源
      await this.nanjingAnjukeScraper.saveListings(allListings);
      console.log(`Anjuke scraper completed. Saved ${allListings.length} total listings (100-110平方: ${listings100110.length}, 110-130平方: ${listings110130.length})`);
    } catch (error) {
      console.error('Error in Anjuke scraper:', error);
      throw error;
    }
  }

  async runNanjingAnjukeOnly(areaFilter?: string, maxPages: number = 3): Promise<void> {
    console.log('Starting Nanjing Anjuke scraper...');
    try {
      const listings = await this.nanjingAnjukeScraper.scrapeAllAreas(areaFilter, maxPages);
      await this.nanjingAnjukeScraper.saveListings(listings);
      console.log(`Nanjing Anjuke scraper completed. Saved ${listings.length} listings`);
    } catch (error) {
      console.error('Error in Nanjing Anjuke scraper:', error);
      throw error;
    }
  }

  async runNanjingAnjukeByArea(areaName: string, areaFilter?: string, maxPages: number = 3): Promise<void> {
    console.log(`Starting Nanjing Anjuke scraper for area: ${areaName}...`);
    try {
      const listings = await this.nanjingAnjukeScraper.scrapeAreaPages(areaName, areaFilter, maxPages);
      await this.nanjingAnjukeScraper.saveListings(listings);
      console.log(`Nanjing Anjuke scraper completed for ${areaName}. Saved ${listings.length} listings`);
    } catch (error) {
      console.error(`Error in Nanjing Anjuke scraper for ${areaName}:`, error);
      throw error;
    }
  }

  private async delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

export { BeikeScraper, NanjingAnjukeScraper };
