import { Elysia } from "elysia";
import { cors } from "@elysiajs/cors";
import { listingController } from "./controllers/listingController";
import { subscriptionController } from "./controllers/subscriptionController";
import { scraperTasks } from "./tasks/scraper";
import 'dotenv/config';

const app = new Elysia()
  // 启用CORS支持前端访问
  .use(cors({
    origin: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE'],
    allowedHeaders: ['Content-Type', 'Authorization']
  }))

  // 健康检查端点
  .get("/", () => ({
    message: "House Spider API is running",
    version: "1.0.0",
    timestamp: new Date().toISOString()
  }))

  // API路由
  .use(listingController)
  .use(subscriptionController)
  .use(scraperTasks)

  // 错误处理
  .onError(({ code, error, set }) => {
    console.error('API Error:', error);

    if (code === 'NOT_FOUND') {
      set.status = 404;
      return { error: 'Route not found' };
    }

    if (code === 'VALIDATION') {
      set.status = 400;
      return { error: 'Validation failed', details: error.message };
    }

    set.status = 500;
    return { error: 'Internal server error' };
  })

  .listen(process.env.PORT || 3000);

console.log(
  `🦊 House Spider API is running at ${app.server?.hostname}:${app.server?.port}`
);
