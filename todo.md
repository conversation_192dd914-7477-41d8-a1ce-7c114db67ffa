

---

### **项目需求文档：个人房产信息聚合与分析平台 (Real Estate Insight Assistant)**

#### **1. 项目概述 (Project Overview)**

**1.1. 项目目标 (Objective)**
开发一个全栈应用程序，旨在自动爬取、监控和分析来自“贝壳网”和“安居客”的房产信息。用户可以查看最新的二手房源，订阅新上房源和关注房源的价格变动，并通过数据可视化的方式进行市场分析。

**1.2. 核心功能 (Core Features)**
- **数据爬取 (Data Scraping):** 定时从指定网站抓取二手房数据。
- **订阅与通知 (Subscription & Notification):** 当符合条件的新房源出现或已关注的房源价格发生变化时，向用户发送通知。
- **数据存储 (Data Persistence):** 将爬取的数据和用户关注信息持久化到数据库中。
- **数据分析与可视化 (Data Analysis & Visualization):** 对房价进行统计分析，并以图表形式展示。
- **Web界面 (Web Interface):** 提供一个现代化的、响应式的用户界面来展示和管理所有信息。

**1.3. 技术栈 (Technology Stack)**
- **后端 (Backend):**
  - **运行时 (Runtime):** Bun
  - **框架 (Framework):** ElysiaJS
  - **数据库ORM (Database ORM):** Prisma (推荐，强类型、自动生成)
  - **爬虫库 (Scraping):** `fetch` (Bun内置), `Cheerio` (类jQuery的HTML解析器)
  - **定时任务 (Scheduled Tasks):** `elysia-cron` (Elysia官方插件)
  - **类型验证 (Type Validation):** `Elysia.t` / `TypeBox` (Elysia内置支持)
- **前端 (Frontend):**
  - **框架 (Framework):** Vue 3 (Composition API)
  - **构建工具 (Build Tool):** Vite
  - **语言 (Language):** TypeScript
  - **UI/CSS:** Tailwind CSS
  - **图表库 (Charting):** `ECharts`
- **数据库 (Database):** PostgreSQL 

---

#### **2. 功能模块详细需求 (Detailed Functional Requirements)**

**2.1. 后端 (Backend - Bun/ElysiaJS)**

**2.1.1. 数据爬取模块 (Scraping Module)**
- **目标网站:**
  - 贝壳网 - 南京二手房: `https://nj.ke.com/ershoufang/`
  - 安居客 - 北京二手房 (移动端): `https://m.anjuke.com/bj/sale/`
- **需要抓取的字段 (Data Fields to Scrape):**
  - `listingId`: 房源唯一ID (非常重要，用于去重和跟踪)
  - `source`: 来源 (e.g., 'beike', 'anjuke')
  - `title`: 房源标题
  - `link`: 房源链接
  - `community`: 小区名称
  - `district`: 所在区域 (e.g., '鼓楼区', '朝阳区')
  - `totalPrice`: 总价 (单位：万)
  - `unitPrice`: 单价 (单位：元/平米)
  - `area`: 面积 (单位：平米)
  - `layout`: 户型 (e.g., '3室1厅')
  - `floor`: 楼层信息
  - `orientation`: 朝向
  - `buildYear`: 建造年份
  - `imageUrl`: 封面图片链接
- **反爬虫策略:**
  - 设置合理的 `User-Agent` 池。
  - 使用代理IP池以避免IP被封禁。
  - 控制请求频率，设置随机延迟。

**2.1.2. 数据库模型 (Database Schema - Prisma)**
- 在项目根目录下创建 `prisma/schema.prisma` 文件。
```prisma
// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql" // or "sqlite"
  url      = env("DATABASE_URL")
}

model HousingListing {
  id               String   @id @default(cuid())
  listingId        String   @unique
  source           String   @db.VarChar(20)
  title            String   @db.VarChar(255)
  link             String   @db.VarChar(512)
  community        String   @db.VarChar(100)
  district         String   @db.VarChar(50)
  totalPrice       Float    // 单位：万
  unitPrice        Float    // 单位：元/平米
  area             Float
  layout           String   @db.VarChar(50)
  floor            String?  @db.VarChar(50)
  orientation      String?  @db.VarChar(50)
  buildYear        Int?
  imageUrl         String?  @db.VarChar(512)
  firstScrapedAt   DateTime @default(now())
  lastScrapedAt    DateTime @updatedAt
  isActive         Boolean  @default(true)

  priceHistory PriceHistory[]
  subscriptions Subscription[]

  @@index([source])
  @@index([community])
  @@index([district])
}

model PriceHistory {
  id         String   @id @default(cuid())
  price      Float    // 总价
  scrapedAt  DateTime @default(now())
  listing    HousingListing @relation(fields: [listingId], references: [id])
  listingId  String

  @@orderBy([scrapedAt(sort: Desc)])
}

enum SubscriptionType {
  NEW_LISTING
  PRICE_CHANGE
}

model Subscription {
  id              String   @id @default(cuid())
  subscriptionType SubscriptionType
  keywords        String?  @db.VarChar(255) // For NEW_LISTING
  isActive        Boolean  @default(true)
  createdAt       DateTime @default(now())

  // Relation to a specific listing for PRICE_CHANGE
  listing         HousingListing? @relation(fields: [listingId], references: [id])
  listingId       String?
}
```

**2.1.3. 定时任务 (Scheduled Tasks - `elysia-cron`)**
- 创建一个 `cron` 任务，定期执行爬虫逻辑。
```typescript
// src/tasks/scraper.ts
import { Elysia } from 'elysia';
import { cron } from '@elysiajs/cron';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// 爬虫核心逻辑函数
async function runScraper() {
  console.log('Starting scraper job...');
  // 1. 爬取贝壳网...
  // 2. 爬取安居客...
  // 3. 对每个房源:
  //    - 使用 prisma.housingListing.upsert() 来创建或更新房源
  //    - 检查价格变化，如果变化则创建新的 PriceHistory
  //    - 检查是否有订阅，触发通知
  console.log('Scraper job finished.');
}

export const scraperTasks = new Elysia()
  .use(
    cron({
      name: 'housing-scraper',
      // 每6小时执行一次
      pattern: '0 */6 * * *', 
      async run() {
        await runScraper();
      }
    })
  );
```

**2.1.4. REST API (ElysiaJS Routes)**
- 使用 Elysia 的路由、中间件和内置验证来构建 API。
```typescript
// src/controllers/listingController.ts
import { Elysia, t } from 'elysia';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export const listingController = new Elysia({ prefix: '/api' })
  // GET /api/listings: 获取房源列表
  .get('/listings', async ({ query }) => {
    // 实现分页、过滤和排序逻辑
    const page = Number(query.page) || 1;
    const limit = Number(query.limit) || 10;
    const listings = await prisma.housingListing.findMany({
      skip: (page - 1) * limit,
      take: limit,
      where: { /* ... filtering logic based on query ... */ }
    });
    return listings;
  }, {
    query: t.Object({
      page: t.Optional(t.Numeric()),
      limit: t.Optional(t.Numeric()),
      // ... other filter params
    })
  })
  // GET /api/listings/:id: 获取房源详情
  .get('/listings/:id', async ({ params }) => {
    return prisma.housingListing.findUnique({
      where: { id: params.id },
      include: { priceHistory: true }
    });
  }, {
    params: t.Object({ id: t.String() })
  })
  // POST /api/subscriptions: 创建订阅
  .post('/subscriptions', async ({ body }) => {
    // ... logic to create a subscription ...
    return await prisma.subscription.create({ data: body });
  }, {
    body: t.Object({
      subscriptionType: t.Enum(SubscriptionType),
      listingId: t.Optional(t.String()),
      keywords: t.Optional(t.String())
    })
  })
  // GET /api/stats/district_avg_price: 获取区域均价
  .get('/stats/district_avg_price', async () => {
    return await prisma.housingListing.groupBy({
      by: ['district'],
      _avg: { unitPrice: true },
      where: { isActive: true }
    });
  });

// 在主文件 (index.ts) 中使用
// const app = new Elysia()
//   .use(listingController)
//   .use(scraperTasks)
//   .listen(3000);
```

**2.2. 前端 (Frontend - Vue 3/Vite/TS/Tailwind/arco.design)**


**2.2.1. 页面/视图 (Pages/Views)**
- **`HomePage.vue`** (首页/仪表盘)
- **`ListingListPage.vue`** (房源列表页)
- **`ListingDetailPage.vue`** (房源详情页)
- **`SubscriptionPage.vue`** (订阅管理页)
- **`AnalysisPage.vue`** (统计分析页)

**2.2.2. 可复用组件 (Reusable Components)**
- `ListingCard.vue`
- `FilterPanel.vue`
- `PriceChart.vue`
- `Navbar.vue`

**2.2.3. 状态管理 (State Management)**
- 使用 `Pinia` 管理全局状态。

**2.2.4. 数据请求 (Data Fetching)**
- 使用 `fetch` API 封装一个类型安全的API客户端。

---

#### **3. 开发计划 (Development Plan - Phased Approach)**

**Phase 1: MVP - 核心功能**
1.  **后端:**
    - 使用 `bun create elysia ./my-app` 初始化项目。
    - 在 `prisma/schema.prisma` 中定义 `HousingListing` 和 `PriceHistory` 模型，运行 `bunx prisma migrate dev` 创建数据库表。
    - 编写核心的爬虫脚本 (使用 `fetch` 和 `cheerio`)，能够手动执行并填充数据库。
    - 创建基础的 Elysia API (`GET /api/listings`)。
2.  **前端:**
    - 搭建 Vue + Vite + TS 项目。
    - 创建 `ListingListPage.vue` 页面，从API获取数据并以列表形式展示。
    - 实现基础的筛选和分页功能。

**Phase 2: 订阅与通知**
1.  **后端:**
    - 集成 `elysia-cron` 插件。
    - 将爬虫脚本转换为定时任务。
    - 实现 `Subscription` Prisma 模型和相关的增删改查 API。
    - 在爬虫任务中加入新房源和价格变动的检测逻辑。
    - 实现一个简单的通知函数 (例如，使用 `nodemailer` 发送邮件或通过 `console.log` 模拟)。
2.  **前端:**
    - 在 `ListingListPage.vue` 和 `ListingDetailPage.vue` 添加“关注”按钮。
    - 创建 `SubscriptionPage.vue` 来管理订阅。

**Phase 3: 数据分析与可视化**
1.  **后端:**
    - 创建用于统计分析的 Elysia 路由 (`/api/stats/...`)。
    - 使用 Prisma 的聚合功能 (`groupBy`, `_avg`, `_count`) 编写查询逻辑。
2.  **前端:**
    - 创建 `AnalysisPage.vue`。
    - 集成 `ECharts` 或 `Chart.js`，调用统计API并渲染图表。
    - 在 `ListingDetailPage.vue` 中添加价格历史图表。

**Phase 4: 完善与优化**
1.  **后端:** 增强爬虫的稳定性和反爬策略（代理、User-Agent轮换）。
2.  **前端:** 优化UI/UX，使用 Tailwind CSS 美化界面，实现响应式设计。
3.  **通用:** 完善错误处理、日志记录。学习如何使用 Docker 打包和部署 Bun/ElysiaJS 应用。

---

这份更新后的文档完全适配了 Bun + ElysiaJS + Prisma 的现代技术栈，为你的项目和 AI 编程助手提供了清晰、准确的开发指南。祝你编码愉快！