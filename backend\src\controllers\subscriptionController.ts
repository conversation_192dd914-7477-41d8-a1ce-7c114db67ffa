import { Elysia, t } from 'elysia';
import { PrismaClient, SubscriptionType } from '@prisma/client';
import { notificationService } from '../services/notificationService';

const prisma = new PrismaClient();

export const subscriptionController = new Elysia({ prefix: '/api' })
  // POST /api/subscriptions: 创建订阅
  .post('/subscriptions', async ({ body }) => {
    try {
      const subscription = await prisma.subscription.create({
        data: {
          subscriptionType: body.subscriptionType,
          keywords: body.keywords,
          listingId: body.listingId,
          isActive: true
        },
        include: {
          listing: body.listingId ? {
            select: {
              id: true,
              title: true,
              community: true,
              district: true,
              totalPrice: true
            }
          } : false
        }
      });

      return subscription;
    } catch (error) {
      console.error('Error creating subscription:', error);
      throw new Error('Failed to create subscription');
    }
  }, {
    body: t.Object({
      subscriptionType: t.Enum(SubscriptionType),
      listingId: t.Optional(t.String()),
      keywords: t.Optional(t.String())
    })
  })

  // GET /api/subscriptions: 获取订阅列表
  .get('/subscriptions', async ({ query }) => {
    try {
      const page = Number(query.page) || 1;
      const limit = Math.min(Number(query.limit) || 20, 100);
      const skip = (page - 1) * limit;

      const where: any = {};
      if (query.type) {
        where.subscriptionType = query.type;
      }
      if (query.active !== undefined) {
        where.isActive = query.active === 'true';
      }

      const [subscriptions, total] = await Promise.all([
        prisma.subscription.findMany({
          where,
          skip,
          take: limit,
          orderBy: { createdAt: 'desc' },
          include: {
            listing: {
              select: {
                id: true,
                title: true,
                community: true,
                district: true,
                totalPrice: true,
                unitPrice: true,
                area: true,
                layout: true
              }
            }
          }
        }),
        prisma.subscription.count({ where })
      ]);

      return {
        data: subscriptions,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      console.error('Error fetching subscriptions:', error);
      throw new Error('Failed to fetch subscriptions');
    }
  }, {
    query: t.Object({
      page: t.Optional(t.Numeric()),
      limit: t.Optional(t.Numeric()),
      type: t.Optional(t.Enum(SubscriptionType)),
      active: t.Optional(t.String())
    })
  })

  // GET /api/subscriptions/:id: 获取订阅详情
  .get('/subscriptions/:id', async ({ params }) => {
    try {
      const subscription = await prisma.subscription.findUnique({
        where: { id: params.id },
        include: {
          listing: {
            include: {
              priceHistory: {
                orderBy: { scrapedAt: 'desc' },
                take: 10
              }
            }
          }
        }
      });

      if (!subscription) {
        throw new Error('Subscription not found');
      }

      return subscription;
    } catch (error) {
      console.error('Error fetching subscription:', error);
      throw new Error('Failed to fetch subscription');
    }
  }, {
    params: t.Object({ id: t.String() })
  })

  // PUT /api/subscriptions/:id: 更新订阅
  .put('/subscriptions/:id', async ({ params, body }) => {
    try {
      const subscription = await prisma.subscription.update({
        where: { id: params.id },
        data: {
          keywords: body.keywords,
          isActive: body.isActive
        },
        include: {
          listing: {
            select: {
              id: true,
              title: true,
              community: true,
              district: true,
              totalPrice: true
            }
          }
        }
      });

      return subscription;
    } catch (error) {
      console.error('Error updating subscription:', error);
      throw new Error('Failed to update subscription');
    }
  }, {
    params: t.Object({ id: t.String() }),
    body: t.Object({
      keywords: t.Optional(t.String()),
      isActive: t.Optional(t.Boolean())
    })
  })

  // DELETE /api/subscriptions/:id: 删除订阅
  .delete('/subscriptions/:id', async ({ params }) => {
    try {
      await prisma.subscription.delete({
        where: { id: params.id }
      });

      return { success: true, message: 'Subscription deleted successfully' };
    } catch (error) {
      console.error('Error deleting subscription:', error);
      throw new Error('Failed to delete subscription');
    }
  }, {
    params: t.Object({ id: t.String() })
  })

  // POST /api/subscriptions/check-new-listings: 检查新房源订阅
  .post('/subscriptions/check-new-listings', async () => {
    try {
      // 获取所有活跃的新房源订阅
      const newListingSubscriptions = await prisma.subscription.findMany({
        where: {
          subscriptionType: SubscriptionType.NEW_LISTING,
          isActive: true
        }
      });

      const notifications = [];

      for (const subscription of newListingSubscriptions) {
        if (!subscription.keywords) continue;

        // 查找匹配关键词的新房源（最近1小时内）
        const oneHourAgo = new Date();
        oneHourAgo.setHours(oneHourAgo.getHours() - 1);

        const matchingListings = await prisma.housingListing.findMany({
          where: {
            AND: [
              { firstScrapedAt: { gte: oneHourAgo } },
              { isActive: true },
              {
                OR: [
                  { title: { contains: subscription.keywords } },
                  { community: { contains: subscription.keywords } },
                  { district: { contains: subscription.keywords } }
                ]
              }
            ]
          }
        });

        if (matchingListings.length > 0) {
          notifications.push({
            subscriptionId: subscription.id,
            type: 'NEW_LISTING',
            keywords: subscription.keywords,
            listings: matchingListings
          });

          // 发送飞书通知
          try {
            await notificationService.sendNewListingNotification(
              matchingListings,
              subscription.keywords || '未指定关键词'
            );
          } catch (error) {
            console.error('发送新房源通知失败:', error);
          }
        }
      }

      return {
        notifications,
        count: notifications.length
      };
    } catch (error) {
      console.error('Error checking new listings:', error);
      throw new Error('Failed to check new listings');
    }
  })

  // POST /api/subscriptions/check-price-changes: 检查价格变动订阅
  .post('/subscriptions/check-price-changes', async () => {
    try {
      // 获取所有活跃的价格变动订阅
      const priceChangeSubscriptions = await prisma.subscription.findMany({
        where: {
          subscriptionType: SubscriptionType.PRICE_CHANGE,
          isActive: true,
          listingId: { not: null }
        },
        include: {
          listing: {
            include: {
              priceHistory: {
                orderBy: { scrapedAt: 'desc' },
                take: 2
              }
            }
          }
        }
      });

      const notifications = [];

      for (const subscription of priceChangeSubscriptions) {
        if (!subscription.listing || subscription.listing.priceHistory.length < 2) continue;

        const [latest, previous] = subscription.listing.priceHistory;
        const priceChange = latest.price - previous.price;

        if (Math.abs(priceChange) > 0.01) { // 价格变动超过0.01万
          notifications.push({
            subscriptionId: subscription.id,
            type: 'PRICE_CHANGE',
            listing: subscription.listing,
            priceChange,
            previousPrice: previous.price,
            currentPrice: latest.price,
            changeDate: latest.scrapedAt
          });

          // 发送飞书通知
          try {
            await notificationService.sendPriceChangeNotification(
              subscription.listing,
              previous.price,
              latest.price
            );
          } catch (error) {
            console.error('发送价格变动通知失败:', error);
          }
        }
      }

      return {
        notifications,
        count: notifications.length
      };
    } catch (error) {
      console.error('Error checking price changes:', error);
      throw new Error('Failed to check price changes');
    }
  })

  // POST /api/subscriptions/test-notification: 测试通知功能
  .post('/subscriptions/test-notification', async () => {
    try {
      const success = await notificationService.sendTestNotification();

      return {
        success,
        message: success ? '测试通知发送成功' : '测试通知发送失败',
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('Error sending test notification:', error);
      return {
        success: false,
        message: '测试通知发送失败',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      };
    }
  });
