
import type { HousingListing, PriceHistory } from '@prisma/client';

interface FeishuMessage {
  msg_type: 'text' | 'rich_text' | 'interactive';
  content: {
    text?: string;
    title?: string;
  };
}

export class NotificationService {
  private feishuWebhookUrl = 'https://open.feishu.cn/open-apis/bot/v2/hook/8c589251-c241-44e1-8644-917c993635c3';

  async sendFeishuMessage(title: string, content: string): Promise<boolean> {
    try {
      const message: FeishuMessage = {
        msg_type: 'text',
        content: {
          text: content,
          title: title
        }
      };

      const response = await fetch(this.feishuWebhookUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(message),
        // Note: fetch does not support timeout natively; can be added with AbortController if needed
      });
      const data = await response.json();
      console.log(data);
      if (data.StatusCode === 0 ) {
        console.log('飞书消息发送成功:', title);
        return true;
      } else {
        console.error('飞书消息发送失败:', data.data);
        return false;
      }
    } catch (error) {
      console.error('发送飞书消息时出错:', error);
      return false;
    }
  }

  async sendNewListingNotification(listings: HousingListing[], keywords: string): Promise<boolean> {
    if (listings.length === 0) return false;

    const title = `🏠 新房源通知 - 关键词: ${keywords}`;
    
    let content = `发现 ${listings.length} 套匹配的新房源：\n\n`;
    
    listings.slice(0, 5).forEach((listing, index) => {
      content += `${index + 1}. ${listing.title}\n`;
      content += `   📍 ${listing.district} - ${listing.community}\n`;
      content += `   💰 ${listing.totalPrice}万 (${listing.unitPrice.toLocaleString()}元/㎡)\n`;
      content += `   📐 ${listing.area}㎡ ${listing.layout}\n`;
      content += `   🔗 ${listing.link}\n\n`;
    });

    if (listings.length > 5) {
      content += `... 还有 ${listings.length - 5} 套房源，请查看系统获取完整信息。`;
    }

    return await this.sendFeishuMessage(title, content);
  }

  async sendPriceChangeNotification(
    listing: HousingListing, 
    oldPrice: number, 
    newPrice: number
  ): Promise<boolean> {
    const priceChange = newPrice - oldPrice;
    const changePercent = ((priceChange / oldPrice) * 100).toFixed(2);
    const changeIcon = priceChange > 0 ? '📈' : '📉';
    const changeText = priceChange > 0 ? '上涨' : '下跌';

    const title = `${changeIcon} 房源价格变动通知`;
    
    const content = `房源价格发生变动：

🏠 ${listing.title}
📍 ${listing.district} - ${listing.community}
📐 ${listing.area}㎡ ${listing.layout}

💰 价格变动：
   原价：${oldPrice}万
   现价：${newPrice}万
   变动：${changeText} ${Math.abs(priceChange).toFixed(1)}万 (${changePercent}%)

🔗 查看详情：${listing.link}

⏰ 变动时间：${new Date().toLocaleString()}`;

    return await this.sendFeishuMessage(title, content);
  }

  async sendScraperSummaryNotification(
    totalListings: number,
    newListings: number,
    priceChanges: number,
    sources: string[]
  ): Promise<boolean> {
    const title = '🤖 爬虫任务完成通知';
    
    const content = `爬虫任务执行完成：

📊 本次统计：
   总房源数：${totalListings}套
   新增房源：${newListings}套
   价格变动：${priceChanges}套

🌐 数据来源：${sources.join(', ')}

⏰ 完成时间：${new Date().toLocaleString()}

💡 您可以登录系统查看详细信息和设置订阅。`;

    return await this.sendFeishuMessage(title, content);
  }

  async sendSystemErrorNotification(error: string, context: string): Promise<boolean> {
    const title = '⚠️ 系统错误通知';
    
    const content = `系统发生错误：

🔍 错误上下文：${context}
❌ 错误信息：${error}
⏰ 发生时间：${new Date().toLocaleString()}

请及时检查系统状态。`;

    return await this.sendFeishuMessage(title, content);
  }

  // 测试通知功能
  async sendTestNotification(): Promise<boolean> {
    const title = '🧪 房产信息聚合平台 - 测试通知';
    const content = `这是一条测试消息，用于验证飞书通知功能是否正常工作。

✅ 如果您收到此消息，说明通知功能已正常配置。

⏰ 发送时间：${new Date().toLocaleString()}`;

    return await this.sendFeishuMessage(title, content);
  }
}

// 导出单例实例
export const notificationService = new NotificationService();
