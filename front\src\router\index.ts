import { createRouter, createWebHistory } from 'vue-router';

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/',
      name: 'Home',
      component: () => import('../views/HomePage.vue'),
      meta: {
        title: '首页'
      }
    },
    {
      path: '/listings',
      name: 'ListingList',
      component: () => import('../views/ListingListPage.vue'),
      meta: {
        title: '房源列表'
      }
    },
    {
      path: '/listings/:id',
      name: 'ListingDetail',
      component: () => import('../views/ListingDetailPage.vue'),
      meta: {
        title: '房源详情'
      }
    },
    {
      path: '/subscriptions',
      name: 'Subscriptions',
      component: () => import('../views/SubscriptionPage.vue'),
      meta: {
        title: '订阅管理'
      }
    },
    {
      path: '/analysis',
      name: 'Analysis',
      component: () => import('../views/AnalysisPage.vue'),
      meta: {
        title: '数据分析'
      }
    },
    {
      path: '/:pathMatch(.*)*',
      name: 'NotFound',
      component: () => import('../views/NotFoundPage.vue'),
      meta: {
        title: '页面未找到'
      }
    }
  ]
});

// 路由守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - 房产信息聚合平台`;
  }
  next();
});

export default router;
