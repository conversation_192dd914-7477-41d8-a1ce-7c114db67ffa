import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { listingApi, type HousingListing, type HousingListingDetail, type ListingFilters, type StatsResponse, type DistrictStats } from '../api/client';

export const useListingsStore = defineStore('listings', () => {
  // 状态
  const listings = ref<HousingListing[]>([]);
  const currentListing = ref<HousingListingDetail | null>(null);
  const loading = ref(false);
  const error = ref<string | null>(null);
  
  // 分页信息
  const pagination = ref({
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0
  });

  // 筛选条件
  const filters = ref<ListingFilters>({
    page: 1,
    limit: 20,
    sortBy: 'lastScrapedAt',
    sortOrder: 'desc' as const
  });

  // 统计数据
  const stats = ref<StatsResponse | null>(null);
  const districtStats = ref<DistrictStats[]>([]);

  // 计算属性
  const hasListings = computed(() => listings.value.length > 0);
  const hasNextPage = computed(() => pagination.value.page < pagination.value.totalPages);
  const hasPrevPage = computed(() => pagination.value.page > 1);

  // 获取房源列表
  const fetchListings = async (newFilters?: Partial<ListingFilters>) => {
    try {
      loading.value = true;
      error.value = null;

      if (newFilters) {
        filters.value = { ...filters.value, ...newFilters };
      }

      const response = await listingApi.getListings(filters.value);
      
      listings.value = response.data;
      pagination.value = response.pagination;
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取房源列表失败';
      console.error('Error fetching listings:', err);
    } finally {
      loading.value = false;
    }
  };

  // 获取房源详情
  const fetchListingDetail = async (id: string) => {
    try {
      loading.value = true;
      error.value = null;

      const listing = await listingApi.getListingDetail(id);
      currentListing.value = listing;
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取房源详情失败';
      console.error('Error fetching listing detail:', err);
    } finally {
      loading.value = false;
    }
  };

  // 获取统计数据
  const fetchStats = async () => {
    try {
      const [statsData, districtData] = await Promise.all([
        listingApi.getStats(),
        listingApi.getDistrictStats()
      ]);
      
      stats.value = statsData;
      districtStats.value = districtData;
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取统计数据失败';
      console.error('Error fetching stats:', err);
    }
  };

  // 更新筛选条件
  const updateFilters = (newFilters: Partial<ListingFilters>) => {
    filters.value = { ...filters.value, ...newFilters, page: 1 };
  };

  // 重置筛选条件
  const resetFilters = () => {
    filters.value = {
      page: 1,
      limit: 20,
      sortBy: 'lastScrapedAt',
      sortOrder: 'desc'
    };
  };

  // 下一页
  const nextPage = () => {
    if (hasNextPage.value) {
      filters.value.page = (filters.value.page || 1) + 1;
      fetchListings();
    }
  };

  // 上一页
  const prevPage = () => {
    if (hasPrevPage.value) {
      filters.value.page = Math.max((filters.value.page || 1) - 1, 1);
      fetchListings();
    }
  };

  // 跳转到指定页
  const goToPage = (page: number) => {
    if (page >= 1 && page <= pagination.value.totalPages) {
      filters.value.page = page;
      fetchListings();
    }
  };

  // 清除当前房源详情
  const clearCurrentListing = () => {
    currentListing.value = null;
  };

  // 清除错误
  const clearError = () => {
    error.value = null;
  };

  return {
    // 状态
    listings,
    currentListing,
    loading,
    error,
    pagination,
    filters,
    stats,
    districtStats,
    
    // 计算属性
    hasListings,
    hasNextPage,
    hasPrevPage,
    
    // 方法
    fetchListings,
    fetchListingDetail,
    fetchStats,
    updateFilters,
    resetFilters,
    nextPage,
    prevPage,
    goToPage,
    clearCurrentListing,
    clearError
  };
});
