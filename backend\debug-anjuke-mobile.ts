import * as cheerio from 'cheerio';

async function debugAnjukeMobile() {
  // 尝试移动版URL
  const urls = [
    'https://m.anjuke.com/nj/sale/jiangning/',
    'https://nanjing.anjuke.com/sale/',
    'https://nanjing.anjuke.com/sale/jiangning/',
    'https://nanjing.anjuke.com/sale/jiangning/p1/',
  ];
  
  for (const url of urls) {
    console.log('\n=== 测试URL:', url, '===');
    
    try {
      const response = await fetch(url, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
          'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
          'Accept-Encoding': 'gzip, deflate, br',
          'Connection': 'keep-alive',
          'Upgrade-Insecure-Requests': '1',
          'Sec-Fetch-Dest': 'document',
          'Sec-Fetch-Mode': 'navigate',
          'Sec-Fetch-Site': 'none',
        }
      });

      console.log('响应状态:', response.status);
      
      const html = await response.text();
      console.log('HTML长度:', html.length);
      
      const $ = cheerio.load(html);
      console.log('页面标题:', $('title').text());
      
      // 检查各种可能的房源容器
      const selectors = [
        '.property',
        '.list-item', 
        '.item-mod',
        '.house-item',
        '.prop-item',
        '.house-list-item',
        '.esf-item',
        '.item',
        '[class*="item"]',
        '[class*="house"]',
        '[class*="property"]'
      ];
      
      for (const selector of selectors) {
        const count = $(selector).length;
        if (count > 0) {
          console.log(`${selector}: ${count} 个元素`);
        }
      }
      
      // 如果HTML很短，可能是验证页面
      if (html.length < 2000) {
        console.log('HTML内容:');
        console.log(html);
      }
      
    } catch (error) {
      console.error('请求失败:', error.message);
    }
  }
}

debugAnjukeMobile().catch(console.error);
