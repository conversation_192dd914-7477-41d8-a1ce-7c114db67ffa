<template>
  <div class="district-price-chart">
    <v-chart
      class="chart"
      :option="chartOption"
      :autoresize="true"
    />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { use } from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import { BarChart } from 'echarts/charts';
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
} from 'echarts/components';
import VChart from 'vue-echarts';
import type { DistrictStats } from '../api/client';

// 注册ECharts组件
use([
  Canvas<PERSON>enderer,
  BarChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
]);

interface Props {
  districtStats: DistrictStats[];
}

const props = defineProps<Props>();

// 计算图表配置
const chartOption = computed(() => {
  // 按平均总价排序，取前10个区域
  const sortedStats = [...props.districtStats]
    .sort((a, b) => (b.avgTotalPrice || 0) - (a.avgTotalPrice || 0))
    .slice(0, 10);

  const districts = sortedStats.map(item => item.district);
  const totalPrices = sortedStats.map(item => item.avgTotalPrice || 0);
  const unitPrices = sortedStats.map(item => (item.avgUnitPrice || 0) / 1000); // 转换为千元/㎡

  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: (params: any) => {
        const totalPrice = params[0];
        const unitPrice = params[1];
        return `
          <div>
            <div style="margin-bottom: 4px; font-weight: 600;">${totalPrice.axisValue}</div>
            <div style="color: #f53f3f;">
              ${totalPrice.marker} 平均总价: ${totalPrice.value}万
            </div>
            <div style="color: #165dff;">
              ${unitPrice.marker} 平均单价: ${(unitPrice.value * 1000).toLocaleString()}元/㎡
            </div>
          </div>
        `;
      },
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#e5e6eb',
      borderWidth: 1,
      textStyle: {
        color: '#1d2129'
      }
    },
    legend: {
      data: ['平均总价', '平均单价'],
      top: 10,
      textStyle: {
        color: '#4e5969'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%',
      containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        data: districts,
        axisPointer: {
          type: 'shadow'
        },
        axisLine: {
          lineStyle: {
            color: '#e5e6eb'
          }
        },
        axisLabel: {
          color: '#86909c',
          fontSize: 12,
          rotate: 45
        },
        axisTick: {
          show: false
        }
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: '总价(万)',
        position: 'left',
        nameTextStyle: {
          color: '#86909c',
          fontSize: 12
        },
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          color: '#86909c',
          fontSize: 12,
          formatter: '{value}万'
        },
        splitLine: {
          lineStyle: {
            color: '#f2f3f5',
            type: 'dashed'
          }
        }
      },
      {
        type: 'value',
        name: '单价(千元/㎡)',
        position: 'right',
        nameTextStyle: {
          color: '#86909c',
          fontSize: 12
        },
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          color: '#86909c',
          fontSize: 12,
          formatter: '{value}k'
        },
        splitLine: {
          show: false
        }
      }
    ],
    series: [
      {
        name: '平均总价',
        type: 'bar',
        yAxisIndex: 0,
        data: totalPrices,
        itemStyle: {
          color: '#f53f3f'
        },
        emphasis: {
          itemStyle: {
            color: '#cb272d'
          }
        }
      },
      {
        name: '平均单价',
        type: 'bar',
        yAxisIndex: 1,
        data: unitPrices,
        itemStyle: {
          color: '#165dff'
        },
        emphasis: {
          itemStyle: {
            color: '#0e42d2'
          }
        }
      }
    ],
    animation: true,
    animationDuration: 1000,
    animationEasing: 'cubicOut'
  };
});
</script>

<style scoped>
.district-price-chart {
  width: 100%;
  height: 100%;
}

.chart {
  width: 100%;
  height: 100%;
}
</style>
