import { NanjingAnjukeScraper } from './src/scrapers/anjuke-nanjing';

async function verifyAreaFilter() {
  const scraper = new NanjingAnjukeScraper();
  
  console.log('=== 验证面积筛选效果 ===\n');
  
  try {
    // 测试1: 无筛选
    console.log('1. 爬取九龙湖无面积筛选...');
    const noFilter = await scraper.scrapePage('九龙湖', undefined, 1);
    console.log(`   爬取到 ${noFilter.length} 条房源`);
    
    if (noFilter.length > 0) {
      const areas = noFilter.map(l => l.area).filter(a => a > 0);
      const minArea = Math.min(...areas);
      const maxArea = Math.max(...areas);
      const avgArea = areas.reduce((a, b) => a + b, 0) / areas.length;
      console.log(`   面积范围: ${minArea}㎡ - ${maxArea}㎡ (平均: ${avgArea.toFixed(1)}㎡)`);
    }
    
    // 测试2: 100-110平方筛选
    console.log('\n2. 爬取九龙湖100-110平方筛选...');
    const filter100110 = await scraper.scrapePage('九龙湖', '100-110平方', 1);
    console.log(`   爬取到 ${filter100110.length} 条房源`);
    
    if (filter100110.length > 0) {
      const areas = filter100110.map(l => l.area).filter(a => a > 0);
      const minArea = Math.min(...areas);
      const maxArea = Math.max(...areas);
      const avgArea = areas.reduce((a, b) => a + b, 0) / areas.length;
      console.log(`   面积范围: ${minArea}㎡ - ${maxArea}㎡ (平均: ${avgArea.toFixed(1)}㎡)`);
      
      // 检查是否在100-110范围内
      const inRange = areas.filter(a => a >= 100 && a <= 110);
      console.log(`   在100-110㎡范围内的房源: ${inRange.length}/${areas.length} (${((inRange.length/areas.length)*100).toFixed(1)}%)`);
    }
    
    // 测试3: 110-130平方筛选
    console.log('\n3. 爬取九龙湖110-130平方筛选...');
    const filter110130 = await scraper.scrapePage('九龙湖', '110-130平方', 1);
    console.log(`   爬取到 ${filter110130.length} 条房源`);
    
    if (filter110130.length > 0) {
      const areas = filter110130.map(l => l.area).filter(a => a > 0);
      const minArea = Math.min(...areas);
      const maxArea = Math.max(...areas);
      const avgArea = areas.reduce((a, b) => a + b, 0) / areas.length;
      console.log(`   面积范围: ${minArea}㎡ - ${maxArea}㎡ (平均: ${avgArea.toFixed(1)}㎡)`);
      
      // 检查是否在110-130范围内
      const inRange = areas.filter(a => a >= 110 && a <= 130);
      console.log(`   在110-130㎡范围内的房源: ${inRange.length}/${areas.length} (${((inRange.length/areas.length)*100).toFixed(1)}%)`);
    }
    
    console.log('\n✅ 面积筛选验证完成');
    
  } catch (error) {
    console.error('❌ 验证失败:', error);
  }
}

verifyAreaFilter().catch(console.error);
