import * as cheerio from 'cheerio';

async function debugCorrectUrl() {
  const url = 'https://nanjing.anjuke.com/sale/jiangninga-q-bjhnj/a16346-p2/';
  
  console.log('调试正确的URL:', url);
  
  try {
    const response = await fetch(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
      }
    });

    console.log('响应状态:', response.status);
    
    const html = await response.text();
    console.log('HTML长度:', html.length);
    
    const $ = cheerio.load(html);
    console.log('页面标题:', $('title').text());
    
    // 检查是否是验证页面
    if (html.includes('安全验证') || html.includes('verifycode') || html.includes('@@xxzlGatewayUrl') || html.length < 10000) {
      console.log('遇到安全验证页面');
      console.log('HTML内容:');
      console.log(html.substring(0, 1000));
      return;
    }
    
    // 检查您提供的页面结构
    console.log('.property 元素数量:', $('.property').length);
    
    if ($('.property').length > 0) {
      console.log('\n找到.property元素，分析第一个:');
      const first = $('.property').first();
      
      console.log('标题:', first.find('.property-content-title-name').text().trim());
      console.log('总价:', first.find('.property-price-total-num').text().trim());
      console.log('单价:', first.find('.property-price-average').text().trim());
      console.log('小区:', first.find('.property-content-info-comm-name').text().trim());
      console.log('链接:', first.find('a').attr('href'));
    } else {
      // 查找其他可能的房源容器
      const selectors = [
        '.list-item',
        '.item-mod',
        '.house-item', 
        '.prop-item',
        '.esf-item',
        '.item',
        '[data-role="item"]',
        '[class*="item"]'
      ];
      
      for (const selector of selectors) {
        const items = $(selector);
        if (items.length > 0) {
          console.log(`\n找到 ${items.length} 个 ${selector} 元素`);
          
          const first = items.first();
          console.log('第一个元素的文本内容:');
          console.log(first.text().substring(0, 200));
          break;
        }
      }
    }
    
  } catch (error) {
    console.error('调试失败:', error);
  }
}

debugCorrectUrl().catch(console.error);
