-- 房源爬虫数据库初始化脚本 (PostgreSQL)

-- 设置字符集
SET client_encoding = 'UTF8';

-- 创建数据库（如果不存在）
-- 注意：在PostgreSQL中，数据库通常通过环境变量POSTGRES_DB创建
-- 这里我们确保数据库存在
SELECT 'CREATE DATABASE house_spider'
WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = 'house_spider');

-- 连接到house_spider数据库
\c house_spider;

-- 设置时区
SET timezone = 'Asia/Shanghai';

-- 创建扩展（如果需要）
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 优化PostgreSQL配置
-- 注意：这些设置可能需要根据实际环境调整
ALTER SYSTEM SET shared_buffers = '128MB';
ALTER SYSTEM SET max_connections = 200;
ALTER SYSTEM SET work_mem = '4MB';
ALTER SYSTEM SET maintenance_work_mem = '64MB';

-- 重新加载配置
SELECT pg_reload_conf();

-- 创建用户权限（如果需要额外用户）
-- PostgreSQL默认使用POSTGRES_USER环境变量创建的用户

-- 设置默认权限
GRANT ALL PRIVILEGES ON DATABASE house_spider TO postgres;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO postgres;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO postgres;
GRANT ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA public TO postgres;

-- 为未来的表设置默认权限
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO postgres;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO postgres;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON FUNCTIONS TO postgres;
