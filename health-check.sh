#!/bin/bash

# 房源爬虫系统健康检查脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查服务健康状态
check_service_health() {
    local service_name=$1
    local url=$2
    local expected_status=${3:-200}
    
    echo -n "检查 $service_name... "
    
    if curl -s -o /dev/null -w "%{http_code}" "$url" | grep -q "$expected_status"; then
        echo -e "${GREEN}✓ 健康${NC}"
        return 0
    else
        echo -e "${RED}✗ 异常${NC}"
        return 1
    fi
}

# 检查容器状态
check_container_status() {
    local container_name=$1
    
    echo -n "检查容器 $container_name... "
    
    if docker ps --format "table {{.Names}}\t{{.Status}}" | grep -q "$container_name.*Up"; then
        echo -e "${GREEN}✓ 运行中${NC}"
        return 0
    else
        echo -e "${RED}✗ 未运行${NC}"
        return 1
    fi
}

# 检查数据库连接
check_database() {
    echo -n "检查数据库连接... "
    
    if docker-compose exec -T mysql mysql -u house_spider_user -phouse_spider_pass_2024 -e "SELECT 1;" house_spider &>/dev/null; then
        echo -e "${GREEN}✓ 连接正常${NC}"
        return 0
    else
        echo -e "${RED}✗ 连接失败${NC}"
        return 1
    fi
}

# 主健康检查函数
main_health_check() {
    echo -e "${BLUE}=== 房源爬虫系统健康检查 ===${NC}"
    echo ""
    
    local all_healthy=true
    
    # 检查容器状态
    echo -e "${YELLOW}1. 容器状态检查${NC}"
    check_container_status "house-spider-mysql" || all_healthy=false
    check_container_status "house-spider-backend" || all_healthy=false
    check_container_status "house-spider-frontend" || all_healthy=false
    echo ""
    
    # 检查服务健康
    echo -e "${YELLOW}2. 服务健康检查${NC}"
    check_service_health "前端服务" "http://localhost" 200 || all_healthy=false
    check_service_health "后端API" "http://localhost:3000" 200 || all_healthy=false
    echo ""
    
    # 检查数据库
    echo -e "${YELLOW}3. 数据库检查${NC}"
    check_database || all_healthy=false
    echo ""
    
    # 显示资源使用情况
    echo -e "${YELLOW}4. 资源使用情况${NC}"
    docker stats --no-stream house-spider-frontend house-spider-backend house-spider-mysql 2>/dev/null || true
    echo ""
    
    # 总结
    if [ "$all_healthy" = true ]; then
        echo -e "${GREEN}✓ 系统整体健康状态: 良好${NC}"
        exit 0
    else
        echo -e "${RED}✗ 系统整体健康状态: 异常${NC}"
        echo ""
        echo -e "${YELLOW}建议操作:${NC}"
        echo "1. 查看日志: ./deploy.sh logs"
        echo "2. 重启服务: ./deploy.sh restart"
        echo "3. 检查配置: cat .env.docker"
        exit 1
    fi
}

# 执行健康检查
main_health_check
