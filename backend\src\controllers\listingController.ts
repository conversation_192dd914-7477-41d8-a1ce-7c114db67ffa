import { Elysia, t } from 'elysia';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export const listingController = new Elysia({ prefix: '/api' })
  // GET /api/listings: 获取房源列表
  .get('/listings', async ({ query }) => {
    try {
      const page = Number(query.page) || 1;
      const limit = Math.min(Number(query.limit) || 20, 100); // 限制最大20条
      const skip = (page - 1) * limit;

      // 构建查询条件
      const where: any = { isActive: true };
      
      if (query.source) {
        where.source = query.source;
      }
      
      if (query.district) {
        where.district = { contains: query.district, mode: 'insensitive' };
      }
      
      if (query.community) {
        where.community = { contains: query.community, mode: 'insensitive' };
      }
      
      if (query.minPrice || query.maxPrice) {
        where.totalPrice = {};
        if (query.minPrice) where.totalPrice.gte = Number(query.minPrice);
        if (query.maxPrice) where.totalPrice.lte = Number(query.maxPrice);
      }
      
      if (query.minArea || query.maxArea) {
        where.area = {};
        if (query.minArea) where.area.gte = Number(query.minArea);
        if (query.maxArea) where.area.lte = Number(query.maxArea);
      }
      
      if (query.layout) {
        where.layout = { contains: query.layout, mode: 'insensitive' };
      }

      // 排序
      const orderBy: any = {};
      if (query.sortBy) {
        const sortField = query.sortBy;
        const sortOrder = query.sortOrder === 'asc' ? 'asc' : 'desc';
        orderBy[sortField] = sortOrder;
      } else {
        orderBy.lastScrapedAt = 'desc'; // 默认按最后抓取时间排序
      }

      // 查询数据
      const [listings, total] = await Promise.all([
        prisma.housingListing.findMany({
          where,
          skip,
          take: limit,
          orderBy,
          select: {
            id: true,
            listingId: true,
            source: true,
            title: true,
            link: true,
            community: true,
            district: true,
            totalPrice: true,
            unitPrice: true,
            area: true,
            layout: true,
            floor: true,
            orientation: true,
            buildYear: true,
            imageUrl: true,
            firstScrapedAt: true,
            lastScrapedAt: true,
          }
        }),
        prisma.housingListing.count({ where })
      ]);

      return {
        data: listings,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      console.error('Error fetching listings:', error);
      throw new Error('Failed to fetch listings');
    }
  }, {
    query: t.Object({
      page: t.Optional(t.Numeric()),
      limit: t.Optional(t.Numeric()),
      source: t.Optional(t.String()),
      district: t.Optional(t.String()),
      community: t.Optional(t.String()),
      minPrice: t.Optional(t.Numeric()),
      maxPrice: t.Optional(t.Numeric()),
      minArea: t.Optional(t.Numeric()),
      maxArea: t.Optional(t.Numeric()),
      layout: t.Optional(t.String()),
      sortBy: t.Optional(t.String()),
      sortOrder: t.Optional(t.Union([t.Literal('asc'), t.Literal('desc')]))
    })
  })

  // GET /api/listings/:id: 获取房源详情
  .get('/listings/:id', async ({ params }) => {
    try {
      const listing = await prisma.housingListing.findUnique({
        where: { id: params.id },
        include: { 
          priceHistory: {
            orderBy: { scrapedAt: 'desc' },
            take: 50 // 最多返回50条价格历史
          }
        }
      });

      if (!listing) {
        throw new Error('Listing not found');
      }

      return listing;
    } catch (error) {
      console.error('Error fetching listing detail:', error);
      throw new Error('Failed to fetch listing detail');
    }
  }, {
    params: t.Object({ id: t.String() })
  })

  // GET /api/stats/summary: 获取统计摘要
  .get('/stats/summary', async () => {
    try {
      const [totalListings, avgPrice, priceRange, sourceStats] = await Promise.all([
        // 总房源数
        prisma.housingListing.count({ where: { isActive: true } }),
        
        // 平均价格
        prisma.housingListing.aggregate({
          where: { isActive: true },
          _avg: { totalPrice: true, unitPrice: true }
        }),
        
        // 价格范围
        prisma.housingListing.aggregate({
          where: { isActive: true },
          _min: { totalPrice: true },
          _max: { totalPrice: true }
        }),
        
        // 按来源统计
        prisma.housingListing.groupBy({
          by: ['source'],
          where: { isActive: true },
          _count: { _all: true }
        })
      ]);

      return {
        totalListings,
        avgTotalPrice: avgPrice._avg.totalPrice,
        avgUnitPrice: avgPrice._avg.unitPrice,
        minPrice: priceRange._min.totalPrice,
        maxPrice: priceRange._max.totalPrice,
        sourceStats: sourceStats.map(stat => ({
          source: stat.source,
          count: stat._count._all
        }))
      };
    } catch (error) {
      console.error('Error fetching summary stats:', error);
      throw new Error('Failed to fetch summary stats');
    }
  })

  // GET /api/stats/district_avg_price: 获取区域均价
  .get('/stats/district_avg_price', async () => {
    try {
      const districtStats = await prisma.housingListing.groupBy({
        by: ['district'],
        where: { isActive: true },
        _avg: { 
          totalPrice: true, 
          unitPrice: true 
        },
        _count: { _all: true },
        orderBy: {
          _avg: {
            totalPrice: 'desc'
          }
        }
      });

      return districtStats.map(stat => ({
        district: stat.district,
        avgTotalPrice: stat._avg.totalPrice,
        avgUnitPrice: stat._avg.unitPrice,
        count: stat._count._all
      }));
    } catch (error) {
      console.error('Error fetching district stats:', error);
      throw new Error('Failed to fetch district stats');
    }
  })

  // GET /api/stats/price_trend: 获取价格趋势
  .get('/stats/price_trend', async ({ query }) => {
    try {
      const days = Number(query.days) || 30;
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);

      const priceTrend = await prisma.priceHistory.groupBy({
        by: ['scrapedAt'],
        where: {
          scrapedAt: { gte: startDate }
        },
        _avg: { price: true },
        _count: { _all: true },
        orderBy: { scrapedAt: 'asc' }
      });

      return priceTrend.map(trend => ({
        date: trend.scrapedAt,
        avgPrice: trend._avg.price,
        count: trend._count._all
      }));
    } catch (error) {
      console.error('Error fetching price trend:', error);
      throw new Error('Failed to fetch price trend');
    }
  }, {
    query: t.Object({
      days: t.Optional(t.Numeric())
    })
  });
