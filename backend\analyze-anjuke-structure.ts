import * as cheerio from 'cheerio';

async function analyzeAnjukeStructure() {
  const url = 'https://nanjing.anjuke.com/sale/jiangning/p1/';
  
  console.log('分析安居客页面结构:', url);
  
  try {
    const response = await fetch(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
      }
    });

    const html = await response.text();
    const $ = cheerio.load(html);
    
    console.log('页面标题:', $('title').text());
    
    // 查找房源列表容器
    const possibleContainers = [
      '.list-item',
      '.item-mod', 
      '.house-item',
      '.prop-item',
      '.property',
      '.esf-item',
      '.house-list-item',
      '.item'
    ];
    
    for (const selector of possibleContainers) {
      const elements = $(selector);
      if (elements.length > 0) {
        console.log(`\n=== ${selector} (${elements.length}个) ===`);
        
        // 分析第一个元素的结构
        const first = elements.first();
        console.log('第一个元素的HTML:');
        console.log(first.html()?.substring(0, 500) + '...');
        
        // 查找可能的标题
        const titleSelectors = ['h3', '.title', '[class*="title"]', 'a'];
        for (const titleSel of titleSelectors) {
          const titleEl = first.find(titleSel).first();
          if (titleEl.length > 0 && titleEl.text().trim()) {
            console.log(`标题 (${titleSel}):`, titleEl.text().trim());
            break;
          }
        }
        
        // 查找可能的价格
        const priceSelectors = ['.price', '[class*="price"]', '[class*="Price"]'];
        for (const priceSel of priceSelectors) {
          const priceEl = first.find(priceSel).first();
          if (priceEl.length > 0 && priceEl.text().trim()) {
            console.log(`价格 (${priceSel}):`, priceEl.text().trim());
            break;
          }
        }
        
        // 查找链接
        const linkEl = first.find('a').first();
        if (linkEl.length > 0) {
          console.log('链接:', linkEl.attr('href'));
        }
        
        break; // 只分析第一个找到的容器
      }
    }
    
    // 查找所有包含房源信息的元素
    console.log('\n=== 查找包含房源关键词的元素 ===');
    const keywords = ['万', '室', '厅', '㎡', '平', '层'];
    
    for (const keyword of keywords) {
      const elements = $(`*:contains("${keyword}")`);
      console.log(`包含"${keyword}"的元素数量:`, elements.length);
      
      if (elements.length > 0 && elements.length < 50) {
        elements.each((i, el) => {
          if (i < 3) { // 只显示前3个
            const text = $(el).text().trim();
            if (text.length < 100) {
              console.log(`  - ${text}`);
            }
          }
        });
      }
    }
    
  } catch (error) {
    console.error('分析过程中出现错误:', error);
  }
}

analyzeAnjukeStructure().catch(console.error);
