<template>
  <div class="source-distribution-chart">
    <v-chart
      class="chart"
      :option="chartOption"
      :autoresize="true"
    />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { use } from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import { PieChart } from 'echarts/charts';
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent
} from 'echarts/components';
import VChart from 'vue-echarts';

// 注册ECharts组件
use([
  Can<PERSON><PERSON><PERSON><PERSON>,
  PieChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent
]);

interface Props {
  sourceStats: Array<{
    source: string;
    count: number;
  }>;
}

const props = defineProps<Props>();

// 获取数据源名称
const getSourceName = (source: string) => {
  switch (source) {
    case 'beike':
      return '贝壳网';
    case 'anjuke':
      return '安居客';
    default:
      return source;
  }
};

// 获取数据源颜色
const getSourceColor = (source: string) => {
  switch (source) {
    case 'beike':
      return '#00b42a';
    case 'anjuke':
      return '#165dff';
    default:
      return '#86909c';
  }
};

// 计算图表配置
const chartOption = computed(() => {
  const data = props.sourceStats.map(item => ({
    name: getSourceName(item.source),
    value: item.count,
    itemStyle: {
      color: getSourceColor(item.source)
    }
  }));

  const total = props.sourceStats.reduce((sum, item) => sum + item.count, 0);

  return {
    tooltip: {
      trigger: 'item',
      formatter: (params: any) => {
        const percent = ((params.value / total) * 100).toFixed(1);
        return `
          <div>
            <div style="margin-bottom: 4px; font-weight: 600;">${params.name}</div>
            <div>
              ${params.marker} 房源数量: ${params.value}套
            </div>
            <div>
              占比: ${percent}%
            </div>
          </div>
        `;
      },
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#e5e6eb',
      borderWidth: 1,
      textStyle: {
        color: '#1d2129'
      }
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      top: 'center',
      textStyle: {
        color: '#4e5969'
      },
      formatter: (name: string) => {
        const item = data.find(d => d.name === name);
        const percent = item ? ((item.value / total) * 100).toFixed(1) : '0';
        return `${name} (${percent}%)`;
      }
    },
    series: [
      {
        name: '数据来源',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['65%', '50%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 16,
            fontWeight: 'bold',
            color: '#1d2129',
            formatter: (params: any) => {
              const percent = ((params.value / total) * 100).toFixed(1);
              return `${params.name}\n${params.value}套\n${percent}%`;
            }
          },
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        },
        labelLine: {
          show: false
        },
        data: data,
        animationType: 'scale',
        animationEasing: 'elasticOut',
        animationDelay: (idx: number) => Math.random() * 200
      }
    ],
    animation: true,
    animationDuration: 1000
  };
});
</script>

<style scoped>
.source-distribution-chart {
  width: 100%;
  height: 100%;
}

.chart {
  width: 100%;
  height: 100%;
}
</style>
