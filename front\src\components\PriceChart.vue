<template>
  <div class="price-chart">
    <v-chart
      class="chart"
      :option="chartOption"
      :autoresize="true"
    />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { use } from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import { LineChart } from 'echarts/charts';
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  DataZoomComponent
} from 'echarts/components';
import VChart from 'vue-echarts';
import type { PriceHistory } from '../api/client';

// 注册ECharts组件
use([
  CanvasRenderer,
  LineChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  DataZoomComponent
]);

interface Props {
  priceHistory: PriceHistory[];
}

const props = defineProps<Props>();

// 计算图表配置
const chartOption = computed(() => {
  // 按时间排序（从早到晚）
  const sortedHistory = [...props.priceHistory].sort(
    (a, b) => new Date(a.scrapedAt).getTime() - new Date(b.scrapedAt).getTime()
  );

  const dates = sortedHistory.map(item => 
    new Date(item.scrapedAt).toLocaleDateString()
  );
  
  const prices = sortedHistory.map(item => item.price);

  return {
    title: {
      text: '价格变动趋势',
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'normal',
        color: '#1d2129'
      }
    },
    tooltip: {
      trigger: 'axis',
      formatter: (params: any) => {
        const data = params[0];
        return `
          <div>
            <div style="margin-bottom: 4px;">${data.axisValue}</div>
            <div style="color: #f53f3f; font-weight: 600;">
              ${data.marker} 总价: ${data.value}万
            </div>
          </div>
        `;
      },
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#e5e6eb',
      borderWidth: 1,
      textStyle: {
        color: '#1d2129'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: dates,
      axisLine: {
        lineStyle: {
          color: '#e5e6eb'
        }
      },
      axisLabel: {
        color: '#86909c',
        fontSize: 12
      },
      axisTick: {
        show: false
      }
    },
    yAxis: {
      type: 'value',
      name: '价格(万)',
      nameTextStyle: {
        color: '#86909c',
        fontSize: 12
      },
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#86909c',
        fontSize: 12,
        formatter: '{value}万'
      },
      splitLine: {
        lineStyle: {
          color: '#f2f3f5',
          type: 'dashed'
        }
      }
    },
    series: [
      {
        name: '总价',
        type: 'line',
        data: prices,
        smooth: true,
        symbol: 'circle',
        symbolSize: 6,
        lineStyle: {
          color: '#165dff',
          width: 3
        },
        itemStyle: {
          color: '#165dff',
          borderColor: '#fff',
          borderWidth: 2
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(22, 93, 255, 0.3)'
              },
              {
                offset: 1,
                color: 'rgba(22, 93, 255, 0.05)'
              }
            ]
          }
        },
        emphasis: {
          itemStyle: {
            color: '#165dff',
            borderColor: '#fff',
            borderWidth: 3,
            shadowColor: 'rgba(22, 93, 255, 0.3)',
            shadowBlur: 10
          }
        }
      }
    ],
    dataZoom: [
      {
        type: 'slider',
        show: sortedHistory.length > 10,
        start: 0,
        end: 100,
        height: 20,
        bottom: '5%',
        textStyle: {
          color: '#86909c'
        },
        borderColor: '#e5e6eb',
        fillerColor: 'rgba(22, 93, 255, 0.1)',
        handleStyle: {
          color: '#165dff'
        }
      }
    ],
    animation: true,
    animationDuration: 1000,
    animationEasing: 'cubicOut'
  };
});
</script>

<style scoped>
.price-chart {
  width: 100%;
  height: 100%;
}

.chart {
  width: 100%;
  height: 100%;
}
</style>
