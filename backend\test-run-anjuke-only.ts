import { ScraperManager } from './src/scrapers';

async function testRunAnjukeOnly() {
  const scraperManager = new ScraperManager();
  
  console.log('=== 测试 runAnjukeOnly 方法 ===\n');
  
  try {
    // 测试爬取所有区域的两种面积筛选
    console.log('开始执行 runAnjukeOnly (每个区域1页)...');
    await scraperManager.runAnjukeOnly(1);
    
    console.log('\n✅ runAnjukeOnly 执行完成！');
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

testRunAnjukeOnly().catch(console.error);
