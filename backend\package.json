{"name": "house-spider-backend", "version": "1.0.50", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "bun run --watch src/index.ts", "db:generate": "bunx prisma generate", "db:migrate": "bunx prisma migrate dev", "db:studio": "bunx prisma studio"}, "dependencies": {"elysia": "latest", "@prisma/client": "^5.22.0", "@elysiajs/cron": "^1.1.0", "@elysiajs/cors": "^1.1.0", "cheerio": "^1.0.0", "dotenv": "^16.4.5"}, "devDependencies": {"bun-types": "latest", "prisma": "^5.22.0", "@types/cheerio": "^0.22.35"}, "module": "src/index.js"}