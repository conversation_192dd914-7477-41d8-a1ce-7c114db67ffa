import { ScraperManager } from './src/scrapers';

/**
 * 南京安居客爬虫使用示例
 * 
 * runAnjukeOnly 方法会自动爬取所有支持区域的两种面积筛选：
 * - 100-110平方 (a16346)
 * - 110-130平方 (a16347)
 * 
 * 支持的区域：
 * - 百家湖 (bjhnj)
 * - 岔路口 (chaluko)
 * - 东山 (njdongshan)
 * - 将军大道 (jiangjundadao)
 * - 九龙湖 (njkaifaqu)
 * - 科学园 (kexueyuan)
 */

async function usageExample() {
  const scraperManager = new ScraperManager();
  
  console.log('=== 南京安居客爬虫使用示例 ===\n');
  
  try {
    // 示例1: 爬取所有区域的两种面积筛选（每个区域2页）
    console.log('示例1: 执行完整的安居客爬取任务...');
    console.log('这将爬取：');
    console.log('- 6个区域 × 2种面积筛选 × 2页 = 24个URL');
    console.log('- 预计获得约400-500条房源信息');
    console.log('');
    
    await scraperManager.runAnjukeOnly(2);
    
    console.log('\n✅ 示例1完成！');
    
    // 示例2: 爬取特定区域的特定面积筛选
    console.log('\n示例2: 爬取百家湖区域的100-110平方房源...');
    await scraperManager.runNanjingAnjukeByArea('百家湖', '100-110平方', 1);
    
    console.log('\n✅ 示例2完成！');
    
    // 示例3: 爬取特定区域的所有面积筛选
    console.log('\n示例3: 爬取九龙湖区域的所有房源（无面积筛选）...');
    await scraperManager.runNanjingAnjukeByArea('九龙湖', undefined, 1);
    
    console.log('\n✅ 示例3完成！');
    
    console.log('\n🎉 所有示例执行完成！');
    console.log('\n数据已保存到数据库，可以通过前端界面查看或直接查询数据库。');
    
  } catch (error) {
    console.error('❌ 执行失败:', error);
  }
}

// 如果直接运行此文件，则执行示例
if (require.main === module) {
  usageExample().catch(console.error);
}

export { usageExample };
