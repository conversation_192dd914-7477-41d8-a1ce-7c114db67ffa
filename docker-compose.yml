version: '3.8'

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: house-spider-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD:-house_spider_root_2024}
      MYSQL_DATABASE: ${MYSQL_DATABASE:-house_spider}
      MYSQL_USER: ${MYSQL_USER:-house_spider_user}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD:-house_spider_pass_2024}
    ports:
      - "7788:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql/init:/docker-entrypoint-initdb.d:ro
    networks:
      - house-spider-network
    command: --default-authentication-plugin=mysql_native_password

  # 后端服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: house-spider-backend
    restart: unless-stopped
    environment:
      DATABASE_URL: mysql://${MYSQL_USER:-house_spider_user}:${MYS<PERSON>_PASSWORD:-house_spider_pass_2024}@mysql:3306/${MYSQL_DATABASE:-house_spider}
      NODE_ENV: production
      SCRAPER_DELAY_MIN: ${SCRAPER_DELAY_MIN:-2000}
      SCRAPER_DELAY_MAX: ${SCRAPER_DELAY_MAX:-5000}
    ports:
      - "3111:3000"
    depends_on:
      - mysql
    networks:
      - house-spider-network
    volumes:
      - ./backend/logs:/app/logs

  # 前端服务
  frontend:
    build:
      context: ./front
      dockerfile: Dockerfile
    container_name: house-spider-frontend
    restart: unless-stopped
    ports:
      - "8891:80"
    depends_on:
      - backend
    networks:
      - house-spider-network

volumes:
  mysql_data:
    driver: local

networks:
  house-spider-network:
    driver: bridge

