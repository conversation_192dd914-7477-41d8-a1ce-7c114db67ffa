<template>
  <a-drawer
    :visible="visible"
    title="筛选条件"
    width="400px"
    @cancel="handleCancel"
    @ok="handleApply"
  >
    <template #footer>
      <a-space>
        <a-button @click="handleReset">重置</a-button>
        <a-button type="primary" @click="handleApply">应用</a-button>
      </a-space>
    </template>

    <div class="filter-content">
      <!-- 数据来源 -->
      <div class="filter-section">
        <h4 class="filter-title">数据来源</h4>
        <a-radio-group v-model="localFilters.source" direction="vertical">
          <a-radio value="">全部</a-radio>
          <a-radio value="beike">贝壳网</a-radio>
          <a-radio value="anjuke">安居客</a-radio>
        </a-radio-group>
      </div>

      <!-- 区域筛选 -->
      <div class="filter-section">
        <h4 class="filter-title">区域</h4>
        <a-input
          v-model="localFilters.district"
          placeholder="请输入区域名称"
          allow-clear
        />
      </div>

      <!-- 小区筛选 -->
      <div class="filter-section">
        <h4 class="filter-title">小区</h4>
        <a-input
          v-model="localFilters.community"
          placeholder="请输入小区名称"
          allow-clear
        />
      </div>

      <!-- 价格范围 -->
      <div class="filter-section">
        <h4 class="filter-title">总价范围 (万)</h4>
        <a-space direction="vertical" fill>
          <a-row :gutter="8">
            <a-col :span="11">
              <a-input-number
                v-model="localFilters.minPrice"
                placeholder="最低价"
                :min="0"
                :precision="0"
                style="width: 100%"
              />
            </a-col>
            <a-col :span="2" class="range-separator">
              <span>-</span>
            </a-col>
            <a-col :span="11">
              <a-input-number
                v-model="localFilters.maxPrice"
                placeholder="最高价"
                :min="0"
                :precision="0"
                style="width: 100%"
              />
            </a-col>
          </a-row>
          
          <!-- 快速选择价格 -->
          <div class="quick-select">
            <a-space wrap>
              <a-tag
                v-for="range in priceRanges"
                :key="range.label"
                :checkable="true"
                :checked="isPriceRangeSelected(range)"
                @check="selectPriceRange(range)"
              >
                {{ range.label }}
              </a-tag>
            </a-space>
          </div>
        </a-space>
      </div>

      <!-- 面积范围 -->
      <div class="filter-section">
        <h4 class="filter-title">面积范围 (㎡)</h4>
        <a-space direction="vertical" fill>
          <a-row :gutter="8">
            <a-col :span="11">
              <a-input-number
                v-model="localFilters.minArea"
                placeholder="最小面积"
                :min="0"
                :precision="0"
                style="width: 100%"
              />
            </a-col>
            <a-col :span="2" class="range-separator">
              <span>-</span>
            </a-col>
            <a-col :span="11">
              <a-input-number
                v-model="localFilters.maxArea"
                placeholder="最大面积"
                :min="0"
                :precision="0"
                style="width: 100%"
              />
            </a-col>
          </a-row>
          
          <!-- 快速选择面积 -->
          <div class="quick-select">
            <a-space wrap>
              <a-tag
                v-for="range in areaRanges"
                :key="range.label"
                :checkable="true"
                :checked="isAreaRangeSelected(range)"
                @check="selectAreaRange(range)"
              >
                {{ range.label }}
              </a-tag>
            </a-space>
          </div>
        </a-space>
      </div>

      <!-- 户型筛选 -->
      <div class="filter-section">
        <h4 class="filter-title">户型</h4>
        <a-space direction="vertical" fill>
          <a-input
            v-model="localFilters.layout"
            placeholder="请输入户型，如：3室2厅"
            allow-clear
          />
          
          <!-- 快速选择户型 -->
          <div class="quick-select">
            <a-space wrap>
              <a-tag
                v-for="layout in commonLayouts"
                :key="layout"
                :checkable="true"
                :checked="localFilters.layout === layout"
                @check="selectLayout(layout)"
              >
                {{ layout }}
              </a-tag>
            </a-space>
          </div>
        </a-space>
      </div>
    </div>
  </a-drawer>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import type { ListingFilters } from '../api/client';

interface Props {
  visible: boolean;
  filters: ListingFilters;
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'apply', filters: Partial<ListingFilters>): void;
  (e: 'reset'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 本地筛选条件
const localFilters = ref<Partial<ListingFilters>>({});

// 价格范围选项
const priceRanges = [
  { label: '100万以下', min: 0, max: 100 },
  { label: '100-200万', min: 100, max: 200 },
  { label: '200-300万', min: 200, max: 300 },
  { label: '300-500万', min: 300, max: 500 },
  { label: '500-800万', min: 500, max: 800 },
  { label: '800万以上', min: 800, max: undefined },
];

// 面积范围选项
const areaRanges = [
  { label: '50㎡以下', min: 0, max: 50 },
  { label: '50-70㎡', min: 50, max: 70 },
  { label: '70-90㎡', min: 70, max: 90 },
  { label: '90-120㎡', min: 90, max: 120 },
  { label: '120-150㎡', min: 120, max: 150 },
  { label: '150㎡以上', min: 150, max: undefined },
];

// 常见户型
const commonLayouts = [
  '1室1厅',
  '2室1厅',
  '2室2厅',
  '3室1厅',
  '3室2厅',
  '4室2厅',
  '5室2厅'
];

// 监听props变化，同步本地筛选条件
watch(
  () => props.filters,
  (newFilters) => {
    localFilters.value = { ...newFilters };
  },
  { immediate: true, deep: true }
);

// 判断价格范围是否被选中
const isPriceRangeSelected = (range: typeof priceRanges[0]) => {
  return localFilters.value.minPrice === range.min && 
         localFilters.value.maxPrice === range.max;
};

// 判断面积范围是否被选中
const isAreaRangeSelected = (range: typeof areaRanges[0]) => {
  return localFilters.value.minArea === range.min && 
         localFilters.value.maxArea === range.max;
};

// 选择价格范围
const selectPriceRange = (range: typeof priceRanges[0]) => {
  if (isPriceRangeSelected(range)) {
    // 取消选择
    localFilters.value.minPrice = undefined;
    localFilters.value.maxPrice = undefined;
  } else {
    // 选择范围
    localFilters.value.minPrice = range.min;
    localFilters.value.maxPrice = range.max;
  }
};

// 选择面积范围
const selectAreaRange = (range: typeof areaRanges[0]) => {
  if (isAreaRangeSelected(range)) {
    // 取消选择
    localFilters.value.minArea = undefined;
    localFilters.value.maxArea = undefined;
  } else {
    // 选择范围
    localFilters.value.minArea = range.min;
    localFilters.value.maxArea = range.max;
  }
};

// 选择户型
const selectLayout = (layout: string) => {
  if (localFilters.value.layout === layout) {
    // 取消选择
    localFilters.value.layout = undefined;
  } else {
    // 选择户型
    localFilters.value.layout = layout;
  }
};

// 事件处理
const handleCancel = () => {
  emit('update:visible', false);
};

const handleApply = () => {
  // 清理空值
  const cleanFilters: Partial<ListingFilters> = {};
  Object.entries(localFilters.value).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      cleanFilters[key as keyof ListingFilters] = value;
    }
  });
  
  emit('apply', cleanFilters);
  emit('update:visible', false);
};

const handleReset = () => {
  localFilters.value = {};
  emit('reset');
};
</script>

<style scoped>
.filter-content {
  padding: 0;
}

.filter-section {
  margin-bottom: 32px;
}

.filter-title {
  font-size: 16px;
  font-weight: 600;
  color: #1d2129;
  margin: 0 0 16px 0;
}

.range-separator {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #86909c;
}

.quick-select {
  margin-top: 12px;
}

:deep(.arco-radio-group) {
  width: 100%;
}

:deep(.arco-radio) {
  margin-bottom: 8px;
}

:deep(.arco-tag) {
  margin-bottom: 8px;
}
</style>
