{"name": "vite-project", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@arco-design/web-vue": "^2.57.0", "@arco-plugins/vite-vue": "^1.4.5", "@tailwindcss/vite": "^4.1.11", "pinia": "^3.0.3", "tailwindcss": "^4.1.11", "vue": "^3.5.17", "vue-router": "^4.5.0", "echarts": "^5.5.1", "vue-echarts": "^7.0.3", "axios": "^1.7.9"}, "devDependencies": {"@vitejs/plugin-vue": "^6.0.0", "@vue/tsconfig": "^0.7.0", "typescript": "~5.8.3", "unplugin-auto-import": "^19.3.0", "unplugin-vue-components": "^28.8.0", "vite": "npm:rolldown-vite@latest", "vue-tsc": "^2.2.12"}}