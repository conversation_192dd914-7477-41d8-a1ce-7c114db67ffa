-- 房源爬虫数据库初始化脚本

-- 设置字符集
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS house_spider 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE house_spider;

-- 创建用户（如果不存在）
CREATE USER IF NOT EXISTS 'house_spider_user'@'%' IDENTIFIED BY 'house_spider_pass_2024';
GRANT ALL PRIVILEGES ON house_spider.* TO 'house_spider_user'@'%';
FLUSH PRIVILEGES;

-- 设置时区
SET time_zone = '+08:00';

-- 优化MySQL配置
SET GLOBAL innodb_buffer_pool_size = 134217728; -- 128MB
SET GLOBAL max_connections = 200;
SET GLOBAL wait_timeout = 28800;
SET GLOBAL interactive_timeout = 28800;

SET FOREIGN_KEY_CHECKS = 1;
