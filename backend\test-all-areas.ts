import { NanjingAnjukeScraper } from './src/scrapers/anjuke-nanjing';

async function testAllAreas() {
  const scraper = new NanjingAnjukeScraper();
  
  console.log('=== 测试所有区域爬取 ===\n');
  
  try {
    // 测试爬取所有区域（每个区域1页）
    console.log('开始爬取所有区域（每个区域1页）...');
    const allListings = await scraper.scrapeAllAreas(undefined, 1);
    
    console.log(`\n✅ 总共爬取到 ${allListings.length} 条房源信息`);
    
    // 按区域统计
    const areaStats = new Map<string, number>();
    allListings.forEach(listing => {
      const count = areaStats.get(listing.district) || 0;
      areaStats.set(listing.district, count + 1);
    });
    
    console.log('\n各区域房源数量统计:');
    for (const [area, count] of areaStats.entries()) {
      console.log(`  ${area}: ${count} 条`);
    }
    
    // 验证数据质量
    const validListings = allListings.filter(listing => 
      listing.title && 
      listing.totalPrice > 0 && 
      listing.area > 0 && 
      listing.layout &&
      listing.community
    );
    
    console.log(`\n数据质量检查:`);
    console.log(`  有效房源: ${validListings.length}/${allListings.length}`);
    console.log(`  完整率: ${((validListings.length / allListings.length) * 100).toFixed(1)}%`);
    
    // 显示一些样本数据
    console.log('\n样本数据:');
    const sampleSize = Math.min(3, validListings.length);
    for (let i = 0; i < sampleSize; i++) {
      const listing = validListings[i];
      console.log(`  ${i + 1}. ${listing.title}`);
      console.log(`     价格: ${listing.totalPrice}万 | 面积: ${listing.area}㎡ | 区域: ${listing.district}`);
      console.log(`     小区: ${listing.community} | 户型: ${listing.layout}`);
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

testAllAreas().catch(console.error);
