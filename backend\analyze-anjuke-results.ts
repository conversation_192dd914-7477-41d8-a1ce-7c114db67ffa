import { NanjingAnjukeScraper } from './src/scrapers/anjuke-nanjing';

async function analyzeAnjukeResults() {
  const scraper = new NanjingAnjukeScraper();
  
  console.log('=== 南京安居客爬取结果分析 ===\n');
  
  try {
    const areas = scraper.getSupportedAreas();
    const filters = scraper.getSupportedAreaFilters();
    
    console.log(`支持的区域 (${areas.length}个):`, areas.join(', '));
    console.log(`支持的面积筛选 (${filters.length}个):`, filters.join(', '));
    
    // 分别测试每个区域的两种面积筛选
    const results = new Map<string, { filter100110: number, filter110130: number }>();
    
    for (const area of areas) {
      console.log(`\n--- 分析 ${area} 区域 ---`);
      
      // 测试100-110平方
      const listings100110 = await scraper.scrapePage(area, '100-110平方', 1);
      console.log(`  100-110平方: ${listings100110.length} 条房源`);
      
      if (listings100110.length > 0) {
        const areas100110 = listings100110.map(l => l.area).filter(a => a > 0);
        const prices100110 = listings100110.map(l => l.totalPrice).filter(p => p > 0);
        
        const avgArea = areas100110.reduce((a, b) => a + b, 0) / areas100110.length;
        const avgPrice = prices100110.reduce((a, b) => a + b, 0) / prices100110.length;
        
        console.log(`    平均面积: ${avgArea.toFixed(1)}㎡`);
        console.log(`    平均价格: ${avgPrice.toFixed(1)}万`);
      }
      
      // 延迟
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // 测试110-130平方
      const listings110130 = await scraper.scrapePage(area, '110-130平方', 1);
      console.log(`  110-130平方: ${listings110130.length} 条房源`);
      
      if (listings110130.length > 0) {
        const areas110130 = listings110130.map(l => l.area).filter(a => a > 0);
        const prices110130 = listings110130.map(l => l.totalPrice).filter(p => p > 0);
        
        const avgArea = areas110130.reduce((a, b) => a + b, 0) / areas110130.length;
        const avgPrice = prices110130.reduce((a, b) => a + b, 0) / prices110130.length;
        
        console.log(`    平均面积: ${avgArea.toFixed(1)}㎡`);
        console.log(`    平均价格: ${avgPrice.toFixed(1)}万`);
      }
      
      results.set(area, {
        filter100110: listings100110.length,
        filter110130: listings110130.length
      });
      
      // 区域间延迟
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    // 总结统计
    console.log('\n=== 总结统计 ===');
    let total100110 = 0;
    let total110130 = 0;
    
    for (const [area, counts] of results.entries()) {
      total100110 += counts.filter100110;
      total110130 += counts.filter110130;
      console.log(`${area}: 100-110平方(${counts.filter100110}条) + 110-130平方(${counts.filter110130}条) = ${counts.filter100110 + counts.filter110130}条`);
    }
    
    console.log(`\n总计:`);
    console.log(`  100-110平方: ${total100110} 条房源`);
    console.log(`  110-130平方: ${total110130} 条房源`);
    console.log(`  合计: ${total100110 + total110130} 条房源`);
    
    console.log(`\n平均每个区域:`);
    console.log(`  100-110平方: ${(total100110 / areas.length).toFixed(1)} 条/区域`);
    console.log(`  110-130平方: ${(total110130 / areas.length).toFixed(1)} 条/区域`);
    
  } catch (error) {
    console.error('❌ 分析失败:', error);
  }
}

analyzeAnjukeResults().catch(console.error);
