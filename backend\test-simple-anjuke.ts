import * as cheerio from 'cheerio';

async function testSimpleAnjuke() {
  const url = 'https://nanjing.anjuke.com/sale/jiangning/';
  
  console.log('测试简化的安居客URL:', url);
  
  try {
    const response = await fetch(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
      }
    });

    console.log('响应状态:', response.status);
    
    const html = await response.text();
    console.log('HTML长度:', html.length);
    
    const $ = cheerio.load(html);
    console.log('页面标题:', $('title').text());
    
    // 检查是否是验证页面
    if (html.includes('安全验证') || html.includes('verifycode') || html.length < 10000) {
      console.log('遇到安全验证页面');
      return;
    }
    
    // 查找房源列表
    const selectors = [
      '.list-item',
      '.item-mod',
      '.house-item', 
      '.prop-item',
      '.property',
      '.esf-item',
      '.item',
      '[data-role="item"]',
      '[class*="item"]'
    ];
    
    let foundItems = false;
    
    for (const selector of selectors) {
      const items = $(selector);
      if (items.length > 0) {
        console.log(`\n找到 ${items.length} 个 ${selector} 元素`);
        foundItems = true;
        
        // 分析前3个元素
        items.slice(0, 3).each((i, el) => {
          const $item = $(el);
          console.log(`\n--- 第${i+1}个元素 ---`);
          
          // 查找标题
          const titleSelectors = ['h3', '.title', '[class*="title"]', 'a'];
          for (const titleSel of titleSelectors) {
            const title = $item.find(titleSel).first().text().trim();
            if (title && title.length > 5) {
              console.log('标题:', title);
              break;
            }
          }
          
          // 查找价格
          const priceSelectors = ['.price', '[class*="price"]', '[class*="Price"]'];
          for (const priceSel of priceSelectors) {
            const price = $item.find(priceSel).first().text().trim();
            if (price && (price.includes('万') || price.includes('元'))) {
              console.log('价格:', price);
              break;
            }
          }
          
          // 查找链接
          const link = $item.find('a').first().attr('href');
          if (link) {
            console.log('链接:', link);
          }
          
          // 查找包含房屋信息的文本
          const text = $item.text();
          const houseInfo = text.match(/\d+室\d+厅|\d+㎡|\d+平/g);
          if (houseInfo) {
            console.log('房屋信息:', houseInfo.join(', '));
          }
        });
        
        break; // 只分析第一个找到的选择器
      }
    }
    
    if (!foundItems) {
      console.log('未找到房源列表元素');
      
      // 输出页面的主要结构
      console.log('\n页面主要结构:');
      $('body').children().each((i, el) => {
        const tagName = el.tagName;
        const className = $(el).attr('class') || '';
        const id = $(el).attr('id') || '';
        console.log(`${tagName}${id ? '#' + id : ''}${className ? '.' + className.split(' ').join('.') : ''}`);
      });
    }
    
  } catch (error) {
    console.error('测试失败:', error);
  }
}

testSimpleAnjuke().catch(console.error);
