import { NanjingAnjukeScraper } from './src/scrapers/anjuke-nanjing';

async function testFullNanjingAnjuke() {
  const scraper = new NanjingAnjukeScraper();
  
  console.log('=== 南京安居客爬虫完整测试 ===\n');
  
  try {
    // 测试1: 爬取百家湖区域（110-100平方）第2页
    console.log('测试1: 爬取百家湖区域（110-100平方）第2页...');
    const baijiahu = await scraper.scrapePage('百家湖', '110-100平方', 2);
    console.log(`✅ 百家湖爬取成功: ${baijiahu.length} 条房源`);
    
    if (baijiahu.length > 0) {
      const sample = baijiahu[0];
      console.log('样本数据:');
      console.log(`  - 标题: ${sample.title}`);
      console.log(`  - 价格: ${sample.totalPrice}万 (${sample.unitPrice}元/㎡)`);
      console.log(`  - 面积: ${sample.area}㎡`);
      console.log(`  - 户型: ${sample.layout}`);
      console.log(`  - 小区: ${sample.community}`);
      console.log(`  - 区域: ${sample.district}`);
    }
    
    // 测试2: 爬取将军大道区域（110-130平方）
    console.log('\n测试2: 爬取将军大道区域（110-130平方）...');
    const jiangjun = await scraper.scrapePage('将军大道', '110-130平方', 1);
    console.log(`✅ 将军大道爬取成功: ${jiangjun.length} 条房源`);
    
    // 测试3: 爬取九龙湖区域多页
    console.log('\n测试3: 爬取九龙湖区域前2页...');
    const jiulonghu = await scraper.scrapeAreaPages('九龙湖', undefined, 2);
    console.log(`✅ 九龙湖多页爬取成功: ${jiulonghu.length} 条房源`);
    
    // 测试4: 验证URL构建
    console.log('\n测试4: 验证URL构建...');
    const testCases = [
      { area: '百家湖', filter: '110-100平方', page: 2, expected: 'https://nanjing.anjuke.com/sale/jiangninga-q-bjhnj/a16346-p2/' },
      { area: '将军大道', filter: '110-130平方', page: 1, expected: 'https://nanjing.anjuke.com/sale/jiangninga-q-jiangjundadao/a16347-p1/' },
      { area: '九龙湖', filter: undefined, page: 3, expected: 'https://nanjing.anjuke.com/sale/jiangninga-q-njkaifaqu/p3/' }
    ];
    
    for (const testCase of testCases) {
      const url = (scraper as any).buildUrl(testCase.area, testCase.filter, testCase.page);
      const isCorrect = url === testCase.expected;
      console.log(`  ${isCorrect ? '✅' : '❌'} ${testCase.area} (${testCase.filter || '无筛选'}) 第${testCase.page}页`);
      console.log(`    生成: ${url}`);
      console.log(`    期望: ${testCase.expected}`);
    }
    
    // 测试5: 数据质量检查
    console.log('\n测试5: 数据质量检查...');
    const allListings = [...baijiahu, ...jiangjun, ...jiulonghu];
    
    const validListings = allListings.filter(listing => 
      listing.title && 
      listing.totalPrice > 0 && 
      listing.area > 0 && 
      listing.layout &&
      listing.community
    );
    
    console.log(`  总房源数: ${allListings.length}`);
    console.log(`  有效房源数: ${validListings.length}`);
    console.log(`  数据完整率: ${((validListings.length / allListings.length) * 100).toFixed(1)}%`);
    
    // 价格范围统计
    const prices = validListings.map(l => l.totalPrice);
    const minPrice = Math.min(...prices);
    const maxPrice = Math.max(...prices);
    const avgPrice = prices.reduce((a, b) => a + b, 0) / prices.length;
    
    console.log(`  价格范围: ${minPrice}万 - ${maxPrice}万`);
    console.log(`  平均价格: ${avgPrice.toFixed(1)}万`);
    
    // 面积范围统计
    const areas = validListings.map(l => l.area);
    const minArea = Math.min(...areas);
    const maxArea = Math.max(...areas);
    const avgArea = areas.reduce((a, b) => a + b, 0) / areas.length;
    
    console.log(`  面积范围: ${minArea}㎡ - ${maxArea}㎡`);
    console.log(`  平均面积: ${avgArea.toFixed(1)}㎡`);
    
    console.log('\n🎉 所有测试完成！南京安居客爬虫工作正常。');
    
  } catch (error) {
    console.error('❌ 测试过程中出现错误:', error);
  }
}

testFullNanjingAnjuke().catch(console.error);
