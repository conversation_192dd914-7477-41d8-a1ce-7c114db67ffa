version: '3.8'

services:
  # PostgreSQL数据库 (注释掉，使用外部数据库)
  # postgres:
  #   image: postgres:15-alpine
  #   container_name: house-spider-postgres
  #   restart: unless-stopped
  #   environment:
  #     POSTGRES_DB: ${POSTGRES_DB:-house_spider}
  #     POSTGRES_USER: ${POSTGRES_USER:-postgres}
  #     POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-18755870538}
  #   ports:
  #     - "5555:5432"
  #   volumes:
  #     - postgres_data:/var/lib/postgresql/data
  #     - ./postgres/init:/docker-entrypoint-initdb.d:ro
  #   networks:
  #     - house-spider-network

  # 后端服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: house-spider-backend
    restart: unless-stopped
    environment:
      DATABASE_URL: postgresql://${POSTGRES_USER:-postgres}:${POSTGRES_PASSWORD:-18755870538}@ali.qianluo.top:5555/${POSTGRES_DB:-house_spider}
      NODE_ENV: production
      SCRAPER_DELAY_MIN: ${SCRAPER_DELAY_MIN:-2000}
      SCRAPER_DELAY_MAX: ${SCRAPER_DELAY_MAX:-5000}
    ports:
      - "3111:3000"
    # depends_on:
    #   - postgres  # 使用外部数据库，不需要依赖
    networks:
      - house-spider-network
    volumes:
      - ./backend/logs:/app/logs

  # 前端服务
  frontend:
    build:
      context: ./front
      dockerfile: Dockerfile
    container_name: house-spider-frontend
    restart: unless-stopped
    ports:
      - "8891:80"
    depends_on:
      - backend
    networks:
      - house-spider-network

# volumes:
  # postgres_data:  # 使用外部数据库，不需要本地数据卷
  #   driver: local

networks:
  house-spider-network:
    driver: bridge

