#!/bin/bash

# 房源爬虫项目Docker部署脚本
# 使用方法: ./deploy.sh [start|stop|restart|logs|status]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}



# 创建必要的目录
create_directories() {
    log_info "创建必要的目录..."
    
    mkdir -p mysql/init
    mkdir -p backend/logs
    
    log_success "目录创建完成"
}

# 启动服务
start_services() {
    log_info "启动房源爬虫服务..."
    
    # 使用.env.docker文件
    export $(cat .env.docker | grep -v '^#' | xargs)
    
    # 构建并启动服务
    sudo docker compose --env-file .env.docker up -d --build
    
    log_success "服务启动完成"
    log_info "前端访问地址: http://localhost:8891"
    log_info "后端API地址: http://localhost:3000"
    log_info "MySQL端口: 7788"
}

# 停止服务
stop_services() {
    log_info "停止房源爬虫服务..."
    
    sudo docker compose down
    
    log_success "服务已停止"
}

# 重启服务
restart_services() {
    log_info "重启房源爬虫服务..."
    
    stop_services
    sleep 2
    start_services
}

# 查看日志
show_logs() {
    log_info "显示服务日志..."
    
    if [ -n "$2" ]; then
        sudo docker compose logs -f "$2"
    else
        sudo docker compose logs -f
    fi
}

# 查看状态
show_status() {
    log_info "服务状态:"
    
    sudo docker compose ps
    
    echo ""
    log_info "容器资源使用情况:"
    docker stats --no-stream house-spider-frontend house-spider-backend house-spider-mysql 2>/dev/null || true
}

# 数据库迁移
migrate_database() {
    log_info "执行数据库迁移..."
    
    sudo docker compose exec backend bunx prisma migrate deploy
    
    log_success "数据库迁移完成"
}

# 清理数据
clean_data() {
    log_warning "这将删除所有数据，包括数据库数据！"
    read -p "确定要继续吗？(y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        log_info "清理数据..."
        sudo docker compose down -v
        docker system prune -f
        log_success "数据清理完成"
    else
        log_info "操作已取消"
    fi
}

# 主函数
main() {
    case "${1:-start}" in
        start)
          
            create_directories
            start_services
            ;;
        stop)
            stop_services
            ;;
        restart)
            restart_services
            ;;
        logs)
            show_logs "$@"
            ;;
        status)
            show_status
            ;;
        migrate)
            migrate_database
            ;;
        clean)
            clean_data
            ;;
        *)
            echo "使用方法: $0 {start|stop|restart|logs|status|migrate|clean}"
            echo ""
            echo "命令说明:"
            echo "  start   - 启动所有服务"
            echo "  stop    - 停止所有服务"
            echo "  restart - 重启所有服务"
            echo "  logs    - 查看日志 (可指定服务名)"
            echo "  status  - 查看服务状态"
            echo "  migrate - 执行数据库迁移"
            echo "  clean   - 清理所有数据"
            echo ""
            echo "示例:"
            echo "  $0 start"
            echo "  $0 logs backend"
            echo "  $0 status"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
