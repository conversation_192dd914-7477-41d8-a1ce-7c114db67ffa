<script setup lang="ts">
import Navbar from './components/Navbar.vue';
</script>

<template>
  <a-layout class="app-layout">
    <!-- 导航栏 -->
    <Navbar />

    <!-- 主要内容区域 -->
    <a-layout-content class="app-content">
      <router-view />
    </a-layout-content>
  </a-layout>
</template>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

#app {
  height: 100%;
}

.app-layout {
  height: 100vh;
}

.app-content {
  overflow-y: auto;
}

/* 全局样式 */
.page-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-container {
    padding: 16px;
  }
}
</style>
