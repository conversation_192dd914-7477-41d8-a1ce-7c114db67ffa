import { BeikeScraper } from './src/scrapers/beike';

async function testScraper() {
  console.log('Testing Beike scraper...');
  
  const scraper = new BeikeScraper();
  
  try {
    // 测试单个URL
    const testUrl = 'https://nj.ke.com/ershoufang/dongshanzhen/co41l3/';
    console.log(`Testing URL: ${testUrl}`);
    
    const listings = await scraper.scrapeUrl(testUrl);
    
    console.log(`Found ${listings.length} listings`);
    
    if (listings.length > 0) {
      console.log('Sample listing:');
      console.log(JSON.stringify(listings[0], null, 2));
    }
    
  } catch (error) {
    console.error('Test failed:', error);
  }
}

testScraper();
