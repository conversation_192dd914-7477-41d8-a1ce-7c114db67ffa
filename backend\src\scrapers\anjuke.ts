import * as cheerio from 'cheerio';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

interface AnjukeListingData {
  listingId: string;
  title: string;
  link: string;
  community: string;
  district: string;
  totalPrice: number;
  unitPrice: number;
  area: number;
  layout: string;
  floor?: string;
  orientation?: string;
  buildYear?: number;
  imageUrl?: string;
}

export class AnjukeScraper {
  private baseUrl = 'https://m.anjuke.com/bj/sale/';
  private userAgents = [
    'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1',
    'Mozilla/5.0 (Android 11; Mobile; rv:68.0) Gecko/68.0 Firefox/88.0',
    'Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36'
  ];

  private getRandomUserAgent(): string {
    return this.userAgents[Math.floor(Math.random() * this.userAgents.length)];
  }

  private async delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private getRandomDelay(): number {
    const min = parseInt(process.env.SCRAPER_DELAY_MIN || '1000');
    const max = parseInt(process.env.SCRAPER_DELAY_MAX || '3000');
    return Math.floor(Math.random() * (max - min + 1)) + min;
  }

  async scrapePage(page: number = 1): Promise<AnjukeListingData[]> {
    try {
      const url = `${this.baseUrl}p${page}/`;
      console.log(`Scraping Anjuke page: ${url}`);

      const response = await fetch(url, {
        headers: {
          'User-Agent': this.getRandomUserAgent(),
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
          'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
          'Accept-Encoding': 'gzip, deflate, br',
          'Connection': 'keep-alive',
          'Upgrade-Insecure-Requests': '1',
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const html = await response.text();
      const $ = cheerio.load(html);
      const listings: AnjukeListingData[] = [];

      $('.list-item').each((index, element) => {
        try {
          const $item = $(element);
          
          // 提取房源ID和链接
          const link = $item.find('.item-title a').attr('href') || '';
          const listingIdMatch = link.match(/\/(\d+)/);
          if (!listingIdMatch) return;
          
          const listingId = `anjuke_${listingIdMatch[1]}`;
          
          // 提取基本信息
          const title = $item.find('.item-title a').text().trim();
          const fullLink = link.startsWith('http') ? link : `https://m.anjuke.com${link}`;
          
          // 提取小区和区域信息
          const locationInfo = $item.find('.item-address').text().trim();
          const locationParts = locationInfo.split(' ');
          const district = locationParts[0] || '';
          const community = locationParts.slice(1).join(' ') || title.split(' ')[0] || '';
          
          // 提取价格信息
          const priceInfo = $item.find('.item-price');
          const totalPriceText = priceInfo.find('.price-num').text().trim();
          const totalPrice = parseFloat(totalPriceText) || 0;
          
          const unitPriceText = priceInfo.find('.price-unit').text().replace(/[^\d]/g, '');
          const unitPrice = parseFloat(unitPriceText) || 0;
          
          // 提取房屋信息
          const houseInfo = $item.find('.item-info').text().trim();
          const houseInfoParts = houseInfo.split('|').map(part => part.trim());
          
          const layout = houseInfoParts[0] || '';
          const areaText = houseInfoParts[1] || '';
          const area = parseFloat(areaText.replace(/[^\d.]/g, '')) || 0;
          const floor = houseInfoParts[2] || '';
          const orientation = houseInfoParts[3] || '';
          const buildYearText = houseInfoParts[4] || '';
          const buildYear = buildYearText ? parseInt(buildYearText.replace(/[^\d]/g, '')) : undefined;
          
          // 提取图片
          const imageUrl = $item.find('.item-img img').attr('src') || 
                          $item.find('.item-img img').attr('data-src') || '';

          if (title && totalPrice > 0 && area > 0) {
            listings.push({
              listingId,
              title,
              link: fullLink,
              community,
              district,
              totalPrice,
              unitPrice,
              area,
              layout,
              floor,
              orientation,
              buildYear,
              imageUrl
            });
          }
        } catch (error) {
          console.error('Error parsing Anjuke listing item:', error);
        }
      });

      console.log(`Scraped ${listings.length} listings from Anjuke page ${page}`);
      return listings;
    } catch (error) {
      console.error(`Error scraping Anjuke page ${page}:`, error);
      return [];
    }
  }

  async scrapeMultiplePages(maxPages: number = 3): Promise<AnjukeListingData[]> {
    const allListings: AnjukeListingData[] = [];
    
    for (let page = 1; page <= maxPages; page++) {
      const listings = await this.scrapePage(page);
      allListings.push(...listings);
      
      // 随机延迟避免被封
      if (page < maxPages) {
        await this.delay(this.getRandomDelay());
      }
    }
    
    return allListings;
  }

  async saveListings(listings: AnjukeListingData[]): Promise<void> {
    for (const listing of listings) {
      try {
        await prisma.housingListing.upsert({
          where: { listingId: listing.listingId },
          update: {
            title: listing.title,
            link: listing.link,
            community: listing.community,
            district: listing.district,
            totalPrice: listing.totalPrice,
            unitPrice: listing.unitPrice,
            area: listing.area,
            layout: listing.layout,
            floor: listing.floor,
            orientation: listing.orientation,
            buildYear: listing.buildYear,
            imageUrl: listing.imageUrl,
            lastScrapedAt: new Date(),
            isActive: true
          },
          create: {
            listingId: listing.listingId,
            source: 'anjuke',
            title: listing.title,
            link: listing.link,
            community: listing.community,
            district: listing.district,
            totalPrice: listing.totalPrice,
            unitPrice: listing.unitPrice,
            area: listing.area,
            layout: listing.layout,
            floor: listing.floor,
            orientation: listing.orientation,
            buildYear: listing.buildYear,
            imageUrl: listing.imageUrl,
            isActive: true
          }
        });

        // 记录价格历史
        const existingListing = await prisma.housingListing.findUnique({
          where: { listingId: listing.listingId },
          include: { priceHistory: { orderBy: { scrapedAt: 'desc' }, take: 1 } }
        });

        if (existingListing && existingListing.priceHistory.length > 0) {
          const lastPrice = existingListing.priceHistory[0].price;
          if (Math.abs(lastPrice - listing.totalPrice) > 0.01) {
            await prisma.priceHistory.create({
              data: {
                listingId: existingListing.id,
                price: listing.totalPrice
              }
            });
          }
        } else if (existingListing) {
          await prisma.priceHistory.create({
            data: {
              listingId: existingListing.id,
              price: listing.totalPrice
            }
          });
        }
      } catch (error) {
        console.error(`Error saving Anjuke listing ${listing.listingId}:`, error);
      }
    }
  }
}
