<template>
  <div class="price-trend-chart">
    <v-chart
      class="chart"
      :option="chartOption"
      :autoresize="true"
    />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { use } from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import { LineChart, Bar<PERSON>hart } from 'echarts/charts';
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  DataZoomComponent
} from 'echarts/components';
import VChart from 'vue-echarts';

// 注册ECharts组件
use([
  Canvas<PERSON><PERSON>er,
  LineChart,
  BarChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  DataZoomComponent
]);

interface Props {
  trendData: Array<{
    date: string;
    avgPrice: number;
    count: number;
  }>;
}

const props = defineProps<Props>();

// 计算图表配置
const chartOption = computed(() => {
  // 按日期排序
  const sortedData = [...props.trendData].sort(
    (a, b) => new Date(a.date).getTime() - new Date(b.date).getTime()
  );

  const dates = sortedData.map(item => 
    new Date(item.date).toLocaleDateString()
  );
  
  const avgPrices = sortedData.map(item => item.avgPrice || 0);
  const counts = sortedData.map(item => item.count || 0);

  return {
    tooltip: {
      trigger: 'axis',
      large: true,
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#999'
        }
      },
      formatter: (params: any) => {
        const priceData = params[0];
        const countData = params[1];
        return `
          <div>
            <div style="margin-bottom: 4px; font-weight: 600;">${priceData.axisValue}</div>
            <div style="color: #f53f3f; margin-bottom: 2px;">
              ${priceData.marker} 平均价格: ${priceData.value}万
            </div>
            <div style="color: #165dff;">
              ${countData.marker} 房源数量: ${countData.value}套
            </div>
          </div>
        `;
      },
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#e5e6eb',
      borderWidth: 1,
      textStyle: {
        color: '#1d2129'
      }
    },
    legend: {
      data: ['平均价格', '房源数量'],
      top: 10,
      textStyle: {
        color: '#4e5969'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      top: '15%',
      containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        data: dates,
        axisPointer: {
          type: 'shadow'
        },
        axisLine: {
          lineStyle: {
            color: '#e5e6eb'
          }
        },
        axisLabel: {
          color: '#86909c',
          fontSize: 12,
          rotate: 45
        },
        axisTick: {
          show: false
        }
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: '平均价格(万)',
        position: 'left',
        nameTextStyle: {
          color: '#86909c',
          fontSize: 12
        },
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          color: '#86909c',
          fontSize: 12,
          formatter: '{value}万'
        },
        splitLine: {
          lineStyle: {
            color: '#f2f3f5',
            type: 'dashed'
          }
        }
      },
      {
        type: 'value',
        name: '房源数量(套)',
        position: 'right',
        nameTextStyle: {
          color: '#86909c',
          fontSize: 12
        },
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          color: '#86909c',
          fontSize: 12,
          formatter: '{value}套'
        },
        splitLine: {
          show: false
        }
      }
    ],
    series: [
      {
        name: '平均价格',
        type: 'line',
        yAxisIndex: 0,
        data: avgPrices,
        smooth: true,
        symbol: 'circle',
        symbolSize: 6,
        lineStyle: {
          color: '#f53f3f',
          width: 3
        },
        itemStyle: {
          color: '#f53f3f',
          borderColor: '#fff',
          borderWidth: 2
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(245, 63, 63, 0.3)'
              },
              {
                offset: 1,
                color: 'rgba(245, 63, 63, 0.05)'
              }
            ]
          }
        },
        emphasis: {
          itemStyle: {
            color: '#f53f3f',
            borderColor: '#fff',
            borderWidth: 3,
            shadowColor: 'rgba(245, 63, 63, 0.3)',
            shadowBlur: 10
          }
        }
      },
      {
        name: '房源数量',
        type: 'bar',
        yAxisIndex: 1,
        data: counts,
        itemStyle: {
          color: 'rgba(22, 93, 255, 0.6)'
        },
        emphasis: {
          itemStyle: {
            color: '#165dff'
          }
        }
      }
    ],
    dataZoom: [
      {
        type: 'slider',
        show: sortedData.length > 15,
        start: 0,
        end: 100,
        height: 20,
        bottom: '5%',
        textStyle: {
          color: '#86909c'
        },
        borderColor: '#e5e6eb',
        fillerColor: 'rgba(22, 93, 255, 0.1)',
        handleStyle: {
          color: '#165dff'
        }
      }
    ],
    animation: true,
    animationDuration: 1000,
    animationEasing: 'cubicOut'
  };
});
</script>

<style scoped>
.price-trend-chart {
  width: 100%;
  height: 100%;
}

.chart {
  width: 100%;
  height: 100%;
}
</style>
