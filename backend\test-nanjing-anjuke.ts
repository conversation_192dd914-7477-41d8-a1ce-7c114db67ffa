import { NanjingAnjukeScraper } from './src/scrapers/anjuke-nanjing';

async function testNanjingAnjukeScraper() {
  const scraper = new NanjingAnjukeScraper();
  
  console.log('=== 南京安居客爬虫测试 ===\n');
  
  // 测试支持的区域
  console.log('支持的区域:', scraper.getSupportedAreas());
  console.log('支持的面积筛选:', scraper.getSupportedAreaFilters());
  console.log('');
  
  try {
    // 测试单个区域爬取（九龙湖，第1页）
    console.log('测试爬取九龙湖区域第1页...');
    const listings = await scraper.scrapePage('九龙湖', undefined, 1);
    
    console.log(`爬取到 ${listings.length} 条房源信息`);
    
    if (listings.length > 0) {
      console.log('\n第一条房源信息:');
      const firstListing = listings[0];
      console.log('- 房源ID:', firstListing.listingId);
      console.log('- 标题:', firstListing.title);
      console.log('- 链接:', firstListing.link);
      console.log('- 小区:', firstListing.community);
      console.log('- 区域:', firstListing.district);
      console.log('- 总价:', firstListing.totalPrice, '万');
      console.log('- 单价:', firstListing.unitPrice, '元/㎡');
      console.log('- 面积:', firstListing.area, '㎡');
      console.log('- 户型:', firstListing.layout);
      console.log('- 楼层:', firstListing.floor);
      console.log('- 朝向:', firstListing.orientation);
      console.log('- 建造年份:', firstListing.buildYear);
      console.log('- 图片URL:', firstListing.imageUrl);
      console.log('- 标签:', firstListing.tags);
      console.log('- VR看房:', firstListing.hasVR);
    }
    
    // 测试带面积筛选的爬取
    console.log('\n测试爬取九龙湖区域（100-110平方）第1页...');
    const filteredListings = await scraper.scrapePage('九龙湖', '100-110平方', 1);
    console.log(`爬取到 ${filteredListings.length} 条房源信息（带面积筛选）`);
    
    // 测试URL构建
    console.log('\n测试URL构建:');
    const testUrls = [
      { area: '九龙湖', filter: undefined, page: 1 },
      { area: '九龙湖', filter: '100-110平方', page: 1 },
      { area: '将军大道', filter: '110-130平方', page: 2 },
      { area: '百家湖', filter: undefined, page: 3 },
      { area: '百家湖', filter: '100-110平方', page: 2 },
      { area: '岔路口', filter: undefined, page: 1 },
      { area: '东山', filter: undefined, page: 1 },
      { area: '科学园', filter: undefined, page: 1 }
    ];
    
    for (const test of testUrls) {
      try {
        // 通过反射访问私有方法进行测试
        const url = (scraper as any).buildUrl(test.area, test.filter, test.page);
        console.log(`${test.area} (${test.filter || '无筛选'}) 第${test.page}页: ${url}`);
      } catch (error) {
        console.error(`构建URL失败 (${test.area}):`, error instanceof Error ? error.message : String(error));
      }
    }
    
  } catch (error) {
    console.error('测试过程中出现错误:', error);
  }
}

// 运行测试
testNanjingAnjukeScraper().catch(console.error);
