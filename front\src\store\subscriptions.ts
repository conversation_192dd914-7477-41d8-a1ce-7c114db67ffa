import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { subscriptionApi, type Subscription } from '../api/client';

export const useSubscriptionsStore = defineStore('subscriptions', () => {
  // 状态
  const subscriptions = ref<Subscription[]>([]);
  const loading = ref(false);
  const error = ref<string | null>(null);
  
  // 分页信息
  const pagination = ref({
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0
  });

  // 计算属性
  const hasSubscriptions = computed(() => subscriptions.value.length > 0);
  const activeSubscriptions = computed(() => 
    subscriptions.value.filter(sub => sub.isActive)
  );
  const newListingSubscriptions = computed(() => 
    subscriptions.value.filter(sub => sub.subscriptionType === 'NEW_LISTING')
  );
  const priceChangeSubscriptions = computed(() => 
    subscriptions.value.filter(sub => sub.subscriptionType === 'PRICE_CHANGE')
  );

  // 获取订阅列表
  const fetchSubscriptions = async (params: {
    page?: number;
    limit?: number;
    type?: 'NEW_LISTING' | 'PRICE_CHANGE';
    active?: boolean;
  } = {}) => {
    try {
      loading.value = true;
      error.value = null;

      const response = await subscriptionApi.getSubscriptions(params);
      
      subscriptions.value = response.data;
      pagination.value = response.pagination;
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取订阅列表失败';
      console.error('Error fetching subscriptions:', err);
    } finally {
      loading.value = false;
    }
  };

  // 创建订阅
  const createSubscription = async (data: {
    subscriptionType: 'NEW_LISTING' | 'PRICE_CHANGE';
    listingId?: string;
    keywords?: string;
  }) => {
    try {
      loading.value = true;
      error.value = null;

      const newSubscription = await subscriptionApi.createSubscription(data);
      
      // 添加到列表开头
      subscriptions.value.unshift(newSubscription);
      pagination.value.total += 1;

      return newSubscription;
    } catch (err) {
      error.value = err instanceof Error ? err.message : '创建订阅失败';
      console.error('Error creating subscription:', err);
      throw err;
    } finally {
      loading.value = false;
    }
  };

  // 更新订阅
  const updateSubscription = async (id: string, data: {
    keywords?: string;
    isActive?: boolean;
  }) => {
    try {
      loading.value = true;
      error.value = null;

      const updatedSubscription = await subscriptionApi.updateSubscription(id, data);
      
      // 更新列表中的订阅
      const index = subscriptions.value.findIndex(sub => sub.id === id);
      if (index !== -1) {
        subscriptions.value[index] = updatedSubscription;
      }

      return updatedSubscription;
    } catch (err) {
      error.value = err instanceof Error ? err.message : '更新订阅失败';
      console.error('Error updating subscription:', err);
      throw err;
    } finally {
      loading.value = false;
    }
  };

  // 删除订阅
  const deleteSubscription = async (id: string) => {
    try {
      loading.value = true;
      error.value = null;

      await subscriptionApi.deleteSubscription(id);
      
      // 从列表中移除
      const index = subscriptions.value.findIndex(sub => sub.id === id);
      if (index !== -1) {
        subscriptions.value.splice(index, 1);
        pagination.value.total -= 1;
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '删除订阅失败';
      console.error('Error deleting subscription:', err);
      throw err;
    } finally {
      loading.value = false;
    }
  };

  // 切换订阅状态
  const toggleSubscriptionStatus = async (id: string) => {
    const subscription = subscriptions.value.find(sub => sub.id === id);
    if (subscription) {
      await updateSubscription(id, { isActive: !subscription.isActive });
    }
  };

  // 清除错误
  const clearError = () => {
    error.value = null;
  };

  return {
    // 状态
    subscriptions,
    loading,
    error,
    pagination,
    
    // 计算属性
    hasSubscriptions,
    activeSubscriptions,
    newListingSubscriptions,
    priceChangeSubscriptions,
    
    // 方法
    fetchSubscriptions,
    createSubscription,
    updateSubscription,
    deleteSubscription,
    toggleSubscriptionStatus,
    clearError
  };
});
