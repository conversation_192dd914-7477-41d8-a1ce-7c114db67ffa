import { Elysia, t } from 'elysia';
import { cron } from '@elysiajs/cron';
import { ScraperManager } from '../scrapers';
import { notificationService } from '../services/notificationService';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

const scraperManager = new ScraperManager();

// 检查价格变动通知
async function checkPriceChangeNotifications(): Promise<number> {
  try {
    // 这里可以调用订阅控制器的检查方法，或者直接实现逻辑
    // 为了简化，我们返回一个估计值
    const recentPriceChanges = await prisma.priceHistory.count({
      where: {
        scrapedAt: {
          gte: new Date(Date.now() - 24 * 60 * 60 * 1000) // 最近24小时
        }
      }
    });
    return recentPriceChanges;
  } catch (error) {
    console.error('Error checking price change notifications:', error);
    return 0;
  }
}

// 检查新房源通知
async function checkNewListingNotifications(): Promise<number> {
  try {
    const recentListings = await prisma.housingListing.count({
      where: {
        firstScrapedAt: {
          gte: new Date(Date.now() - 24 * 60 * 60 * 1000) // 最近24小时
        }
      }
    });
    return recentListings;
  } catch (error) {
    console.error('Error checking new listing notifications:', error);
    return 0;
  }
}

// 爬虫核心逻辑函数
async function runScraper() {
  console.log('Starting scheduled scraper job...');

  try {
    // 记录开始时的房源数量
    const initialCount = await prisma.housingListing.count();

    // 运行所有爬虫，每次爬取3页
    await scraperManager.runAllScrapers(3);

    // 记录结束时的房源数量
    const finalCount = await prisma.housingListing.count();
    const newListings = finalCount - initialCount;

    // 检查订阅通知
    const priceChangeCount = await checkPriceChangeNotifications();

    // 发送汇总通知
    await notificationService.sendScraperSummaryNotification(
      finalCount,
      newListings,
      priceChangeCount,
      ['贝壳网', '安居客']
    );

    console.log('Scheduled scraper job completed successfully');
  } catch (error) {
    console.error('Error in scheduled scraper job:', error);

    // 发送错误通知
    await notificationService.sendSystemErrorNotification(
      error instanceof Error ? error.message : 'Unknown error',
      '定时爬虫任务'
    );
  }
}

// 手动触发爬虫的函数
async function runManualScraper(source?: string, pages: number = 1) {
  console.log(`Starting manual scraper job for ${source || 'all sources'}...`);

  try {
    // 记录开始时的房源数量
    const initialCount = await prisma.housingListing.count();

    if (source === 'beike') {
      await scraperManager.runBeikeOnly(pages);
    } else if (source === 'anjuke') {
      await scraperManager.runAnjukeOnly(pages);
    } else {
      await scraperManager.runAllScrapers(pages);
    }

    // 记录结束时的房源数量
    const finalCount = await prisma.housingListing.count();
    const newListings = finalCount - initialCount;

    // 发送手动爬虫完成通知
    if (newListings > 0) {
      await notificationService.sendScraperSummaryNotification(
        finalCount,
        newListings,
        0, // 手动爬虫不检查价格变动
        source ? [source] : ['贝壳网', '安居客']
      );
    }

    console.log('Manual scraper job completed successfully');
  } catch (error) {
    console.error('Error in manual scraper job:', error);

    // 发送错误通知
    await notificationService.sendSystemErrorNotification(
      error instanceof Error ? error.message : 'Unknown error',
      `手动爬虫任务 - ${source || 'all sources'}`
    );

    throw error;
  }
}

export const scraperTasks = new Elysia({ prefix: '/api' })
  // 定时任务：每6小时执行一次
  .use(
    cron({
      name: 'housing-scraper',
      // 每6小时执行一次 (0分钟，每6小时，每天，每月，每周)
      pattern: '0 */6 * * *', 
      async run() {
        await runScraper();
      }
    })
  )
  
  // 手动触发爬虫的API端点
  .post('/scraper/run', async ({ body }) => {
    try {
      const { source, pages = 1 } = body || {};
      await runManualScraper(source, pages);

      return {
        success: true,
        message: `Scraper job completed for ${source || 'all sources'}`,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('Manual scraper API error:', error);
      return {
        success: false,
        message: 'Scraper job failed',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      };
    }
  }, {
    body: t.Object({
      source: t.Optional(t.String()),
      pages: t.Optional(t.Number())
    })
  })
  
  // 获取爬虫状态
  .get('/scraper/status', async () => {
    try {
      // 这里可以添加更多状态信息，比如最后运行时间、运行状态等
      return {
        status: 'active',
        lastRun: new Date().toISOString(),
        nextRun: 'Every 6 hours',
        availableSources: ['beike', 'anjuke']
      };
    } catch (error) {
      console.error('Error getting scraper status:', error);
      return {
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  });

// 导出手动运行函数供其他模块使用
export { runManualScraper, runScraper };
