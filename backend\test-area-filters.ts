import { NanjingAnjukeScraper } from './src/scrapers/anjuke-nanjing';

async function testAreaFilters() {
  const scraper = new NanjingAnjukeScraper();
  
  console.log('=== 测试面积筛选 ===\n');
  
  console.log('支持的面积筛选:', scraper.getSupportedAreaFilters());
  
  // 测试URL构建
  console.log('\n测试面积筛选URL构建:');
  const testCases = [
    { area: '百家湖', filter: '100-110平方', page: 1 },
    { area: '百家湖', filter: '110-130平方', page: 2 },
    { area: '九龙湖', filter: '100-110平方', page: 1 },
    { area: '将军大道', filter: '110-130平方', page: 1 }
  ];
  
  for (const testCase of testCases) {
    try {
      const url = (scraper as any).buildUrl(testCase.area, testCase.filter, testCase.page);
      console.log(`${testCase.area} (${testCase.filter}) 第${testCase.page}页:`);
      console.log(`  ${url}`);
    } catch (error) {
      console.error(`构建URL失败 (${testCase.area}, ${testCase.filter}):`, error instanceof Error ? error.message : String(error));
    }
  }
  
  // 测试实际爬取
  console.log('\n测试实际爬取:');
  
  try {
    console.log('1. 测试爬取百家湖100-110平方...');
    const listings1 = await scraper.scrapePage('百家湖', '100-110平方', 1);
    console.log(`   爬取到 ${listings1.length} 条房源`);
    
    console.log('2. 测试爬取九龙湖110-130平方...');
    const listings2 = await scraper.scrapePage('九龙湖', '110-130平方', 1);
    console.log(`   爬取到 ${listings2.length} 条房源`);
    
    if (listings2.length > 0) {
      const sample = listings2[0];
      console.log('   样本数据:');
      console.log(`   - 标题: ${sample.title}`);
      console.log(`   - 面积: ${sample.area}㎡`);
      console.log(`   - 价格: ${sample.totalPrice}万`);
      console.log(`   - 户型: ${sample.layout}`);
    }
    
  } catch (error) {
    console.error('爬取测试失败:', error);
  }
}

testAreaFilters().catch(console.error);
