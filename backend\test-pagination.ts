import { BeikeScraper } from './src/scrapers/beike';

async function testPagination() {
  console.log('Testing Beike scraper pagination...');
  
  const scraper = new BeikeScraper();
  
  try {
    // 测试单个URL的分页
    const testUrl1 = 'https://nj.ke.com/ershoufang/chalukou1/pg1co41l3/';
    const testUrl2 = 'https://nj.ke.com/ershoufang/chalukou1/pg2co41l3/';
    
    console.log(`Testing page 1: ${testUrl1}`);
    const page1Listings = await scraper.scrapeUrl(testUrl1);
    console.log(`Page 1 found: ${page1Listings.length} listings`);
    
    if (page1Listings.length > 0) {
      console.log('Sample listing from page 1:');
      console.log({
        title: page1Listings[0].title,
        district: page1Listings[0].district,
        totalPrice: page1Listings[0].totalPrice,
        area: page1Listings[0].area
      });
    }
    
    console.log(`\nTesting page 2: ${testUrl2}`);
    const page2Listings = await scraper.scrapeUrl(testUrl2);
    console.log(`Page 2 found: ${page2Listings.length} listings`);
    
    // 测试多页爬取
    console.log('\nTesting multi-page scraping (2 pages per area)...');
    const allListings = await scraper.scrapeMultiplePages(2);
    console.log(`Total listings found: ${allListings.length}`);
    
    // 按区域统计
    const stats = allListings.reduce((acc, listing) => {
      acc[listing.district] = (acc[listing.district] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    console.log('\nListings by district:');
    Object.entries(stats).forEach(([district, count]) => {
      console.log(`${district}: ${count} listings`);
    });
    
  } catch (error) {
    console.error('Test failed:', error);
  }
}

testPagination();
