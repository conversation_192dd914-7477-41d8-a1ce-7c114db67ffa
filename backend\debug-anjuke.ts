import * as cheerio from 'cheerio';

async function debugAnjukePage() {
  const url = 'https://nanjing.anjuke.com/sale/jiangninga-q-njkaifaqu/o4-p1/?from=esf_prop';
  
  console.log('调试安居客页面:', url);
  
  try {
    const response = await fetch(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Referer': 'https://nanjing.anjuke.com/',
      }
    });

    console.log('响应状态:', response.status);
    console.log('响应头:', Object.fromEntries(response.headers.entries()));
    
    const html = await response.text();
    console.log('HTML长度:', html.length);
    
    // 检查是否包含预期的元素
    const $ = cheerio.load(html);
    
    console.log('页面标题:', $('title').text());
    console.log('.property 元素数量:', $('.property').length);
    console.log('.property-content-title-name 元素数量:', $('.property-content-title-name').length);
    
    // 查看页面中是否有其他可能的房源容器
    console.log('.list-item 元素数量:', $('.list-item').length);
    console.log('.item-mod 元素数量:', $('.item-mod').length);
    console.log('.house-item 元素数量:', $('.house-item').length);
    console.log('.prop-item 元素数量:', $('.prop-item').length);
    
    // 输出前1000个字符的HTML内容
    console.log('\nHTML内容前1000字符:');
    console.log(html.substring(0, 1000));
    
    // 查找所有包含"房"字的class
    const allElements = $('*');
    const housingRelatedClasses = new Set<string>();
    
    allElements.each((i, el) => {
      const className = $(el).attr('class');
      if (className && (className.includes('房') || className.includes('property') || className.includes('house') || className.includes('item'))) {
        housingRelatedClasses.add(className);
      }
    });
    
    console.log('\n可能相关的CSS类名:');
    Array.from(housingRelatedClasses).forEach(cls => console.log('-', cls));
    
  } catch (error) {
    console.error('调试过程中出现错误:', error);
  }
}

debugAnjukePage().catch(console.error);
